# Python virtual environments
.venv
venv/
env/

# Python cache files
__pycache__/
*.pyc
*.pyo
*.pyd

# Development files
requirements-dev.txt
.git/
.gitignore
*.md
deploy.sh
deploy-existing.sh
setup-secrets.sh
.vscode/
.idea/

# Cache and logs
cache/
logs/
*.log

# Test files
tests/
test_*.py
*_test.py

# Documentation
docs/
DEPLOYMENT_GUIDE.md
API_CONSOLIDATION_SUMMARY.md
CONSOLIDATED_API_ENDPOINTS.md
MIGRATION_GUIDE.md
SFDC_PROXY_ARCHITECTURE.md

# Pipeline files
pipeline-*.yml
azure-pipelines.yml

# Scripts
scripts/