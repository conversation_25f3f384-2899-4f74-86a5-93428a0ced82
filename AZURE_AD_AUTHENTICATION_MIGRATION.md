# Azure AD Authentication Migration

## Overview

This document outlines the changes made to migrate from email/password authentication to Azure AD-only authentication, and provides solutions for the Key Vault access issues.

## Changes Made

### 1. Removed Email/Password Authentication Endpoints

**Backend Endpoints Removed:**
- `POST /auth/login` - Basic email/password login
- `POST /auth/signup` - User registration  
- `POST /users/login` - User login endpoint
- `POST /users/login/verify` - Login verification
- `POST /users/{user_id}/login` - Create user login credentials

**Endpoints Retained:**
- `GET /auth/azure/login` - Azure AD login redirect
- `GET /auth/azure/callback` - Azure AD callback handler
- `GET /auth/azure/me` - Get current user info

### 2. Updated Documentation

- Updated `CONSOLIDATED_API_ENDPOINTS.md` to reflect Azure AD-only authentication
- Updated `function_app.py` endpoint listings
- Added notes about removed functionality

## Current Issue: Key Vault Access

### Problem
The backend is failing when accessing the `list_integrations` endpoint with this error:

```
Error retrieving secret 'jwt-secret': (Forbidden) Caller is not authorized to perform action on resource.
Caller: appid=123d7a1b-7b24-4924-a73c-1fbcff016b12
Action: 'Microsoft.KeyVault/vaults/secrets/getSecret/action'
Resource: '/subscriptions/35518353-3fc5-49c1-91cd-3ab90df8d78d/resourcegroups/atomsec-dev-data/providers/microsoft.keyvault/vaults/akv-atomsec-dev/secrets/jwt-secret'
```

### Root Cause
The managed identity (appid=123d7a1b-7b24-4924-a73c-1fbcff016b12) doesn't have permission to access the `jwt-secret` in the Key Vault `akv-atomsec-dev`.

### Solution Options

#### Option 1: Grant Key Vault Access (Recommended)

Grant the managed identity access to the Key Vault secret:

```bash
# Get the managed identity object ID
az ad sp show --id 123d7a1b-7b24-4924-a73c-1fbcff016b12 --query objectId -o tsv

# Grant Key Vault Secrets User role to the managed identity
az role assignment create \
  --role "Key Vault Secrets User" \
  --assignee-object-id <OBJECT_ID_FROM_ABOVE> \
  --scope "/subscriptions/35518353-3fc5-49c1-91cd-3ab90df8d78d/resourcegroups/atomsec-dev-data/providers/microsoft.keyvault/vaults/akv-atomsec-dev"
```

Or use Key Vault access policies:

```bash
az keyvault set-policy \
  --name akv-atomsec-dev \
  --object-id <OBJECT_ID_FROM_ABOVE> \
  --secret-permissions get list
```

#### Option 2: Use Environment Variables for Development

For development environments, you can set the JWT secret as an environment variable:

```bash
# In local.settings.json or environment variables
JWT_SECRET=your-jwt-secret-here
```

Then modify the `get_jwt_secret()` function to check environment variables first.

## Frontend Changes Required

Since this repository contains only the backend, the frontend changes need to be made in the frontend repository:

### 1. Remove Login/Signup Forms
- Remove email/password input fields
- Remove login/signup form components
- Remove related validation logic

### 2. Keep Only Azure AD Authentication
- Keep "Sign in with Microsoft" button
- Ensure it redirects to `/auth/azure/login` endpoint
- Handle the callback at `/auth-callback` route

### 3. Update API Calls
- Remove calls to removed endpoints (`/auth/login`, `/auth/signup`)
- Ensure all API calls include proper Azure AD tokens

## Testing the Changes

### 1. Test Azure AD Authentication Flow
```bash
# Test Azure AD login redirect
curl -X GET "http://localhost:7072/api/db/auth/azure/login"

# Should redirect to Microsoft login page
```

### 2. Test Removed Endpoints
```bash
# These should return 404 or method not allowed
curl -X POST "http://localhost:7072/api/db/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}'

curl -X POST "http://localhost:7072/api/db/auth/signup" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}'
```

### 3. Test Integration Access (After Key Vault Fix)
```bash
# This should work after Key Vault permissions are fixed
curl -X GET "http://localhost:7072/api/db/integrations" \
  -H "Authorization: Bearer <AZURE_AD_TOKEN>"
```

## Automated Fix Scripts

Two scripts have been created to automatically fix the Key Vault permissions:

### Bash Script (Linux/macOS)
```bash
./scripts/fix_keyvault_permissions.sh
```

### PowerShell Script (Windows)
```powershell
.\scripts\fix_keyvault_permissions.ps1
```

Both scripts will:
1. Verify Azure CLI is installed and you're logged in
2. Get the managed identity object ID
3. Grant Key Vault Secrets User role via RBAC
4. Set Key Vault access policy as fallback
5. Provide testing instructions

## Code Changes Made

### 1. Enhanced JWT Configuration
- Modified `shared/config.py` to check environment variables before Key Vault
- Added better error handling for Key Vault access failures
- Improved logging for troubleshooting

### 2. Improved Error Handling
- Updated `shared/auth_utils.py` with better JWT secret error handling
- Enhanced token decoding error messages
- More graceful failure modes

### 3. Removed Endpoints
- `api/auth_endpoints.py`: Removed `POST /auth/login`
- `api/user_endpoints.py`: Removed `POST /auth/signup`, `POST /users/login`, `POST /users/login/verify`, `POST /users/{user_id}/login`

## Next Steps

1. **Immediate**: Run the Key Vault permission fix script
   ```bash
   ./scripts/fix_keyvault_permissions.sh
   ```

2. **Alternative**: Set JWT secret as environment variable temporarily
   ```bash
   # In Azure Function App settings or local.settings.json
   JWT_SECRET=your-secure-jwt-secret-here
   ```

3. **Frontend**: Remove login/signup forms in the frontend repository
   - Remove email/password input components
   - Remove login/signup form validation
   - Keep only "Sign in with Microsoft" button
   - Update API calls to remove references to removed endpoints

4. **Testing**: Verify Azure AD authentication flow works end-to-end

5. **Cleanup**: Remove unused password-related methods from `UserRepository` if desired

## Security Considerations

- Azure AD provides better security than custom email/password authentication
- Centralized identity management through Azure Entra ID
- No need to store password hashes in the database
- Leverages Microsoft's security infrastructure
- JWT tokens are still used for session management after Azure AD authentication
