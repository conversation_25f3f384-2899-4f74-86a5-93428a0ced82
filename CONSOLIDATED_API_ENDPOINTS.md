# Consolidated Database Service API Endpoints

## Base URL
- **Local Development**: `http://localhost:7072/api/db`
- **Production**: `https://<function-app-name>.azurewebsites.net/api/db`

## Authentication Endpoints

```
GET  /auth/azure/login                    - Azure AD login redirect
GET  /auth/azure/callback                 - Azure AD callback handler
GET  /auth/azure/me                       - Get current user info
POST /auth/login                          - Standard login
POST /auth/signup                         - User registration
```

## Integration Management (CRUD)

```
GET    /integrations                      - List integrations with filtering
POST   /integrations                      - Create new integration
GET    /integrations/{id}                 - Get integration by ID
PUT    /integrations/{id}                 - Update integration
DELETE /integrations/{id}                 - Delete integration (soft delete)
```

### Query Parameters for Listing
- `user_email` - Filter by user email
- `user_id` - Filter by user ID
- `account_id` - Filter by account ID
- `include_inactive` - Include inactive integrations (default: false)
- `integration_type` - Filter by integration type

## Integration Data Endpoints

```
GET /integrations/{id}/overview           - Get integration overview
GET /integrations/{id}/health-check       - Get health check data
GET /integrations/{id}/profiles           - Get profiles and permission sets
GET /integrations/{id}/pmd-issues         - Get PMD scan issues
```

## Integration Actions

```
POST /integration/test-connection         - Test Salesforce connection
POST /integration/connect                 - Connect and store credentials
POST /integration/scan/{id}               - Enqueue scan task for integration
```

## User Management

```
GET    /users                             - List users
POST   /users                             - Create new user
GET    /users/{id}                        - Get user by ID
PUT    /users/{id}                        - Update user
DELETE /users/{id}                        - Delete user
GET    /users/email/{email}               - Get user by email
POST   /users/{id}/login                  - Create user login
POST   /users/login/verify                - Verify user login
GET    /user/profile                      - Get current user profile
PUT    /user/profile                      - Update current user profile
```

## Account Management

```
GET    /accounts                          - List accounts
POST   /accounts                          - Create new account
GET    /accounts/{id}                     - Get account by ID
PUT    /accounts/{id}                     - Update account
DELETE /accounts/{id}                     - Delete account
```

## Organization Management

```
GET    /organizations                     - List organizations
POST   /organizations                     - Create new organization
GET    /organizations/{id}                - Get organization by ID
PUT    /organizations/{id}                - Update organization
DELETE /organizations/{id}                - Delete organization
```

## Task Management

```
GET    /tasks                             - List tasks
POST   /tasks                             - Create new task
GET    /tasks/{id}                        - Get task by ID
PUT    /tasks/{id}/status                 - Update task status
```

### Query Parameters for Task Listing
- `org_id` - Filter by organization ID
- `status` - Filter by task status
- `task_type` - Filter by task type

## Security Data

```
GET  /security/health-checks              - List health checks
POST /security/health-checks              - Store health check data
GET  /security/profiles                   - List security profiles
POST /security/profiles                   - Store profile data
GET  /security/overview                   - Get security overview
```

## System Endpoints

```
GET /health                               - Health check endpoint
GET /info                                 - Service information
```

## Response Format

All endpoints return JSON responses with the following structure:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "count": 10  // For list endpoints
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message description"
}
```

## HTTP Status Codes

- `200` - Success
- `201` - Created
- `202` - Accepted (for async operations)
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

## Authentication

Most endpoints require authentication. Include the authorization header:

```
Authorization: Bearer <access_token>
```

## Rate Limiting

Currently no rate limiting is implemented, but it's recommended for production use.

## Data Formats

### Integration Object
```json
{
  "id": "uuid",
  "name": "Integration Name",
  "tenant_url": "https://example.salesforce.com",
  "type": "Salesforce",
  "description": "Description",
  "environment": "production",
  "is_active": true,
  "last_scan": "2024-01-01T00:00:00Z",
  "last_scan_attempt": "2024-01-01T00:00:00Z",
  "health_score": 85,
  "created_at": "2024-01-01T00:00:00Z",
  "user_email": "<EMAIL>",
  "user_id": "user-id",
  "account_id": 1
}
```

### Task Object
```json
{
  "id": "task-id",
  "task_type": "sfdc_authenticate",
  "org_id": "integration-id",
  "user_id": "user-id",
  "status": "Pending",
  "priority": "High",
  "created_at": "2024-01-01T00:00:00Z",
  "data": {
    // Task-specific data
  }
}
```

### User Object
```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "name": "User Name",
  "is_active": true,
  "account_id": 1,
  "created_at": "2024-01-01T00:00:00Z"
}
```

## Notes

1. All timestamps are in ISO 8601 format
2. UUIDs are used for integration IDs
3. Soft deletes are used (setting `is_active` to false)
4. Local development uses Azure Table Storage
5. Production uses SQL Database
6. All endpoints support CORS for frontend integration
