# AtomSec Database Function App - Deployment Guide

This guide provides comprehensive instructions for deploying the AtomSec Database Function App to Azure.

## 🚀 Quick Start

### Option 1: Use Existing Pipeline (Recommended)
If you have access to the Azure DevOps pipeline, this is the easiest option:

1. **Trigger the Pipeline**:
   - Push changes to `dev` or `dev-db` branch
   - The pipeline will automatically deploy to `func-atomsec-dbconnect-dev.azurewebsites.net`

2. **Monitor Deployment**:
   - Check the pipeline logs in Azure DevOps
   - Test the health endpoint: `https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db/health`

### Option 2: Manual Deployment
For new environments or manual deployment:

## 📋 Prerequisites

### Required Tools
- **Azure CLI** (v2.50+): `az --version`
- **Azure Functions Core Tools** (v4.x): `func --version`
- **Python** (3.12): `python --version`
- **Git**: For source control

### Azure Subscription Requirements
- Active Azure subscription
- Contributor access to resource group
- Ability to create Function Apps, Storage Accounts, and Key Vaults

## 🏗️ Azure Resources Setup

### 1. Create Resource Group
```bash
# Set variables
RESOURCE_GROUP="atomsec-prod-data"
LOCATION="East US"
FUNCTION_APP_NAME="func-atomsec-dbconnect-prod"
STORAGE_ACCOUNT="statomsecprod$(date +%s)"
KEY_VAULT_NAME="akv-atomsec-prod"

# Create resource group
az group create --name $RESOURCE_GROUP --location "$LOCATION"
```

### 2. Create Storage Account
```bash
# Create storage account
az storage account create \
  --name $STORAGE_ACCOUNT \
  --resource-group $RESOURCE_GROUP \
  --location "$LOCATION" \
  --sku Standard_LRS \
  --kind StorageV2

# Get connection string
STORAGE_CONNECTION=$(az storage account show-connection-string \
  --name $STORAGE_ACCOUNT \
  --resource-group $RESOURCE_GROUP \
  --query connectionString -o tsv)
```

### 3. Create Key Vault
```bash
# Create Key Vault
az keyvault create \
  --name $KEY_VAULT_NAME \
  --resource-group $RESOURCE_GROUP \
  --location "$LOCATION" \
  --sku standard

# Set access policy for your user
az keyvault set-policy \
  --name $KEY_VAULT_NAME \
  --upn $(az account show --query user.name -o tsv) \
  --secret-permissions get list set delete
```

### 4. Create Function App
```bash
# Create Function App
az functionapp create \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --storage-account $STORAGE_ACCOUNT \
  --runtime python \
  --runtime-version 3.12 \
  --functions-version 4 \
  --os-type Linux \
  --consumption-plan-location "$LOCATION"
```

## ⚙️ Configuration

### 1. Set Application Settings
```bash
# Configure Function App settings
az functionapp config appsettings set \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --settings \
    "AZURE_STORAGE_CONNECTION_STRING=$STORAGE_CONNECTION" \
    "KEY_VAULT_NAME=$KEY_VAULT_NAME" \
    "ENVIRONMENT=production" \
    "IS_LOCAL_DEV=false" \
    "FUNCTIONS_WORKER_RUNTIME=python"
```

### 2. Configure Managed Identity
```bash
# Enable system-assigned managed identity
az functionapp identity assign \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP

# Get the principal ID
PRINCIPAL_ID=$(az functionapp identity show \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --query principalId -o tsv)

# Grant Key Vault access to the Function App
az keyvault set-policy \
  --name $KEY_VAULT_NAME \
  --object-id $PRINCIPAL_ID \
  --secret-permissions get list
```

### 3. Store Secrets in Key Vault
```bash
# Store required secrets
az keyvault secret set --vault-name $KEY_VAULT_NAME --name "jwt-secret" --value "your-production-jwt-secret"
az keyvault secret set --vault-name $KEY_VAULT_NAME --name "sql-connection-string" --value "your-sql-connection-string"
az keyvault secret set --vault-name $KEY_VAULT_NAME --name "azure-ad-client-id" --value "your-azure-ad-client-id"
az keyvault secret set --vault-name $KEY_VAULT_NAME --name "azure-ad-client-secret" --value "your-azure-ad-client-secret"
az keyvault secret set --vault-name $KEY_VAULT_NAME --name "azure-ad-tenant-id" --value "your-azure-ad-tenant-id"
```

## 🚢 Deployment

### Method 1: Azure Functions Core Tools
```bash
# Navigate to project directory
cd /path/to/atomsec-func-db-r

# Install dependencies locally (optional, for validation)
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -r requirements.txt

# Deploy to Azure
func azure functionapp publish $FUNCTION_APP_NAME --python
```

### Method 2: Azure CLI with ZIP Deploy
```bash
# Create deployment package
zip -r deployment.zip . -x "*.git*" "*__pycache__*" "*.venv*" "*node_modules*"

# Deploy using ZIP
az functionapp deployment source config-zip \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --src deployment.zip
```

## ✅ Verification

### 1. Check Function App Status
```bash
# Check if Function App is running
az functionapp show \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "state"
```

### 2. Test Endpoints
```bash
# Test health endpoint
curl "https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/health"

# Test info endpoint
curl "https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/info"
```

### 3. Check Logs
```bash
# Stream live logs
az webapp log tail --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP

# Download logs
az webapp log download --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP
```

## 🔧 Troubleshooting

### Common Issues

1. **Import Errors**:
   - Check that all dependencies are in requirements.txt
   - Verify Python version compatibility (3.12)

2. **Authentication Errors**:
   - Verify managed identity is enabled
   - Check Key Vault access policies
   - Ensure secrets exist in Key Vault

3. **Database Connection Issues**:
   - Verify SQL connection string in Key Vault
   - Check firewall rules for Azure SQL Database
   - Test connection from Azure portal

4. **CORS Issues**:
   - Update CORS settings in host.json
   - Add your domain to allowed origins

### Debug Commands
```bash
# Check Function App configuration
az functionapp config show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP

# List application settings
az functionapp config appsettings list --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP

# Check deployment status
az webapp deployment list --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP
```

## 🔄 CI/CD Pipeline Setup

If you want to create a new pipeline or modify the existing one, see the `pipeline-func-db-dev.yml` file as a reference.

Key pipeline steps:
1. Install Python and dependencies
2. Install ODBC drivers for SQL Server
3. Run tests (optional)
4. Create deployment package
5. Deploy to Function App
6. Run post-deployment tests

## 📚 Next Steps

After successful deployment:
1. Set up monitoring with Application Insights
2. Configure alerts for health checks
3. Set up backup and disaster recovery
4. Implement proper logging and monitoring
5. Set up staging environment for testing

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Azure Function App logs
3. Verify all prerequisites are met
4. Check Azure service health status
