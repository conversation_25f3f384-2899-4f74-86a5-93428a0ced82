# Migration Guide: Transitioning to AtomSec Database Service

This guide provides step-by-step instructions for migrating from the monolithic AtomSec function app to the new microservices architecture with a dedicated database service.

## Overview

The migration involves:
1. Setting up the new database function app
2. Updating the main function app to use the database service
3. Testing the integration
4. Deploying both services

## Phase 1: Setup Database Service

### 1.1 Local Development Setup

1. Navigate to the database function app directory:
```bash
cd atomsec-func-db
```

2. Install dependencies:
```bash
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

3. Start the database service:
```bash
func start --port 7072
```

### 1.2 Verify Database Service

Test the health endpoint:
```bash
curl http://localhost:7072/api/db/health
```

## Phase 2: Update Main Function App

### 2.1 Create Database Service Client

Create a new file `atomsec-func-sfdc/shared/db_service_client.py`:

```python
import os
import requests
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class DatabaseServiceClient:
    """Client for interacting with the database service"""
    
    def __init__(self):
        self.base_url = os.environ.get('DB_SERVICE_URL', 'http://localhost:7072/api/db')
        self.timeout = 30
        
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to database service"""
        url = f"{self.base_url}/{endpoint}"
        
        try:
            response = requests.request(
                method=method,
                url=url,
                json=data,
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Database service request failed: {str(e)}")
            raise
    
    # User operations
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email"""
        try:
            result = self._make_request('GET', f'users/email/{email}')
            return result.get('data') if result.get('success') else None
        except:
            return None
    
    def create_user(self, user_data: Dict[str, Any]) -> Optional[int]:
        """Create new user"""
        try:
            result = self._make_request('POST', 'users', user_data)
            return result.get('data', {}).get('user_id') if result.get('success') else None
        except:
            return None
    
    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> bool:
        """Update user"""
        try:
            result = self._make_request('PUT', f'users/{user_id}', user_data)
            return result.get('success', False)
        except:
            return False
    
    # Add more methods as needed...

# Global instance
db_client = DatabaseServiceClient()
```

### 2.2 Update Existing Code

Replace direct database calls with service calls. For example:

**Before (in blueprints/auth.py):**
```python
from shared.user_repository import create_user_account, get_user_account_by_email

# Direct database call
user = get_user_account_by_email(email)
```

**After:**
```python
from shared.db_service_client import db_client

# Service call
user = db_client.get_user_by_email(email)
```

### 2.3 Environment Configuration

Add the database service URL to `local.settings.json`:

```json
{
  "Values": {
    "DB_SERVICE_URL": "http://localhost:7072/api/db"
  }
}
```

## Phase 3: Repository Migration Mapping

### User Operations

| Old Function | New Service Endpoint | Method |
|-------------|---------------------|---------|
| `get_user_account_by_email(email)` | `/users/email/{email}` | GET |
| `get_user_account_by_id(user_id)` | `/users/{user_id}` | GET |
| `create_user_account(...)` | `/users` | POST |
| `update_user(...)` | `/users/{user_id}` | PUT |
| `create_user_login(...)` | `/users/{user_id}/login` | POST |
| `verify_user_login(...)` | `/users/login/verify` | POST |

### Organization Operations (To be implemented)

| Old Function | New Service Endpoint | Method |
|-------------|---------------------|---------|
| `get_org_table_repo().query_entities()` | `/organizations` | GET |
| `get_org_table_repo().insert_entity()` | `/organizations` | POST |
| `get_org_table_repo().update_entity()` | `/organizations/{org_id}` | PUT |

## Phase 4: Testing

### 4.1 Integration Tests

Create integration tests to verify both services work together:

```python
# tests/test_integration.py
import pytest
import requests

DB_SERVICE_URL = "http://localhost:7072/api/db"
MAIN_SERVICE_URL = "http://localhost:7071/api"

def test_user_creation_flow():
    # Create user via main service
    response = requests.post(f"{MAIN_SERVICE_URL}/auth/signup", json={
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "name": "Test User"
    })
    assert response.status_code == 201
    
    # Verify user exists in database service
    response = requests.get(f"{DB_SERVICE_URL}/users/email/<EMAIL>")
    assert response.status_code == 200
    assert response.json()["data"]["email"] == "<EMAIL>"
```

### 4.2 Load Testing

Test the performance impact of the service separation:

```bash
# Install locust
pip install locust

# Run load test
locust -f tests/load_test.py --host=http://localhost:7071
```

## Phase 5: Deployment

### 5.1 Deploy Database Service

1. Create Function App in Azure:
```bash
az functionapp create --resource-group atomsec-rg \
  --consumption-plan-location westus2 \
  --runtime python \
  --runtime-version 3.9 \
  --functions-version 4 \
  --name atomsec-func-db \
  --storage-account atomsecstore
```

2. Configure app settings:
```bash
az functionapp config appsettings set --name atomsec-func-db \
  --resource-group atomsec-rg \
  --settings "KEY_VAULT_NAME=akv-atomsec-dev" \
  "AZURE_TENANT_ID=<tenant-id>" \
  "AZURE_CLIENT_ID=<client-id>"
```

3. Deploy the code:
```bash
cd atomsec-func-db
func azure functionapp publish atomsec-func-db
```

### 5.2 Update Main Function App

1. Add database service URL to app settings:
```bash
az functionapp config appsettings set --name atomsec-func-sfdc \
  --resource-group atomsec-rg \
  --settings "DB_SERVICE_URL=https://atomsec-func-db.azurewebsites.net/api/db"
```

2. Deploy updated code:
```bash
cd atomsec-func-sfdc
func azure functionapp publish atomsec-func-sfdc
```

## Phase 6: Monitoring and Rollback

### 6.1 Monitor Services

Set up Application Insights to monitor both services:
- Response times
- Error rates
- Dependencies between services

### 6.2 Rollback Plan

If issues arise:
1. Keep the old database access code in a feature flag
2. Switch back to direct database access if needed
3. Gradually migrate endpoints one by one

## Best Practices

1. **Gradual Migration**: Migrate one module at a time
2. **Feature Flags**: Use feature flags to switch between old and new implementations
3. **Monitoring**: Set up comprehensive monitoring before migration
4. **Documentation**: Update API documentation for both services
5. **Error Handling**: Implement proper error handling for service communication

## Common Issues and Solutions

### Issue 1: Timeout Errors
**Solution**: Increase timeout values and implement retry logic

### Issue 2: Authentication Between Services
**Solution**: Implement service-to-service authentication using managed identities

### Issue 3: Data Consistency
**Solution**: Implement proper transaction handling across services

## Next Steps

1. Complete implementation of all repository classes
2. Add caching layer to reduce database calls
3. Implement circuit breaker pattern for resilience
4. Set up automated deployment pipelines
5. Create comprehensive API documentation 