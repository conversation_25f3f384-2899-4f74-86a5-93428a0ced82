# SFDC Proxy Architecture

## Overview

The DB service now acts as a centralized proxy for all frontend requests, including those that need to be forwarded to the SFDC service. This architecture change ensures:

1. **Single Entry Point**: All frontend requests go through the DB service
2. **Centralized CORS**: CORS is handled consistently in one place
3. **Simplified Routing**: Azure API Management only needs to route to the DB service
4. **Business Logic Separation**: SFDC service retains all business logic while DB service handles data operations

## Architecture Flow

```
Frontend → Azure API Management → DB Service → SFDC Service (when needed)
                                      ↓
                                  Database
```

## Proxy Endpoints

The DB service now provides proxy endpoints that forward requests to the SFDC service:

### Integration Management
- `POST /v1/integration/scan/{id}` → `POST /api/integration/scan/{id}` (SFDC)

### Task Management
- `GET /v1/task-status` → `GET /api/task-status` (SFDC)
- `POST /v1/tasks/cancel` → `POST /api/tasks/cancel` (SFDC)
- `POST /v1/tasks/schedule` → `POST /api/tasks/schedule` (SFDC)

### Security Analysis
- `GET /v1/health-score` → `GET /api/health-score` (SFDC)
- `GET /v1/health-risks` → `GET /api/health-risks` (SFDC)
- `GET /v1/profiles` → `GET /api/profiles` (SFDC)
- `GET /v1/permission-sets` → `GET /api/permission-sets` (SFDC)

### Scan Results
- `GET /v1/scan/accounts` → `GET /api/scan/accounts` (SFDC)
- `GET /v1/scan/history` → `GET /api/scan/history` (SFDC)

### System Endpoints
- `GET /v1/sfdc/health` → `GET /api/health` (SFDC)
- `POST /v1/sfdc/info` → `POST /api/info` (SFDC)

## Implementation Details

### SFDC Service Client
- **File**: `shared/sfdc_service_client.py`
- **Purpose**: Handles HTTP communication with the SFDC service
- **Features**: 
  - Automatic URL construction
  - Error handling and logging
  - Authentication header forwarding
  - Timeout management (2 minutes for SFDC operations)

### Proxy Endpoints
- **File**: `api/sfdc_proxy_endpoints.py`
- **Purpose**: Provides proxy endpoints that forward requests to SFDC service
- **Features**:
  - CORS handling for all endpoints
  - Authentication header forwarding
  - Request/response proxying
  - Error handling and logging

### Configuration
- **Local Development**: SFDC service at `http://localhost:7071`
- **Production**: SFDC service through Azure API Management
- **Environment Variable**: `SFDC_SERVICE_URL`

## Frontend Changes

The frontend configuration has been updated to route all requests through the DB service:

### Before (Mixed Services)
```javascript
// Some endpoints went to SFDC service
scan: '/api/integration/scan/{id}',  // SFDC service
taskstatus: '/api/task-status',      // SFDC service

// Some endpoints went to DB service
list: getDbServiceUrl('/integrations'), // DB service
```

### After (Centralized)
```javascript
// All endpoints go through DB service
scan: getDbServiceUrl('/integration/scan/{id}'),  // DB service → SFDC
taskstatus: getDbServiceUrl('/task-status'),      // DB service → SFDC
list: getDbServiceUrl('/integrations'),           // DB service
```

## Benefits

1. **Simplified CORS**: Only need to configure CORS for the DB service
2. **Centralized Authentication**: All auth headers flow through one service
3. **Easier Deployment**: Azure API Management only routes to DB service
4. **Better Monitoring**: All requests can be logged and monitored in one place
5. **Flexible Routing**: Can easily change which service handles which endpoints

## Deployment Considerations

1. **DB Service**: Must be deployed with SFDC proxy endpoints
2. **SFDC Service**: Continues to handle business logic, no changes needed
3. **Frontend**: Updated configuration routes everything through DB service
4. **Azure API Management**: Only needs to route `/v1/*` to DB service

## Testing

To test the proxy functionality:

1. **Start both services locally**:
   ```bash
   # Terminal 1: Start SFDC service
   cd atomsec-func-sfdc
   func start --port 7071

   # Terminal 2: Start DB service
   cd atomsec-func-db-r
   func start --port 7072
   ```

2. **Test proxy endpoints**:
   ```bash
   # Test health score proxy
   curl http://localhost:7072/v1/health-score

   # Test task status proxy
   curl http://localhost:7072/v1/task-status
   ```

3. **Verify frontend integration**:
   - Start frontend: `npm start`
   - Check that all API calls go to `http://localhost:7072/v1/*`
   - Verify that SFDC functionality still works through the proxy

## Troubleshooting

### Common Issues

1. **SFDC Service Not Available**: Check that SFDC service is running and accessible
2. **CORS Errors**: Verify CORS configuration in DB service
3. **Authentication Issues**: Ensure auth headers are being forwarded correctly
4. **Timeout Errors**: Check SFDC service response times (2-minute timeout)

### Logging

All proxy requests are logged with:
- Request method and endpoint
- SFDC service URL being called
- Response status and any errors
- Authentication headers (sanitized)
