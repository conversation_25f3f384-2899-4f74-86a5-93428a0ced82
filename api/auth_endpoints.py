"""
Azure AD Authentication Endpoints

This module provides Azure AD authentication endpoints for the AtomSec application.
Moved from atomsec-func-sfdc to centralize authentication in the database service.

Endpoints:
- GET /auth/azure/login - Initiate Azure AD login
- GET /auth/azure/callback - Handle Azure AD callback
- GET /auth/azure/me - Get current user information
"""

import logging
import json
import os
import secrets
import requests
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional
import urllib.parse
import azure.functions as func
from shared.cors_middleware import add_cors_headers

logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Constants
STATE_TIMEOUT_MINUTES = 30
STATES = {}  # In-memory state storage (replace with more durable storage in production)

# Azure AD Configuration
def get_azure_ad_config():
    """Get Azure AD configuration"""
    return {
        'client_id': os.environ.get('AZURE_CLIENT_ID', '2d313c1a-d62d-492c-869e-cf8cb9258204'),
        'tenant_id': os.environ.get('AZURE_TENANT_ID', '41b676db-bf6f-46ae-a354-a83a1362533f'),
        'client_secret': os.environ.get('AZURE_CLIENT_SECRET', ''),
        'authority': f"https://login.microsoftonline.com/{os.environ.get('AZURE_TENANT_ID', '41b676db-bf6f-46ae-a354-a83a1362533f')}",
        'scope': 'openid profile email User.Read',
        'redirect_uri': os.environ.get('AZURE_REDIRECT_URI', 'http://localhost:7072/api/db/auth/azure/callback')
    }

def cleanup_expired_states():
    """Clean up expired state values"""
    now = datetime.now(timezone.utc)
    expired_states = [state for state, data in STATES.items()
                     if now > data.get("expires_at", now)]
    
    for state in expired_states:
        STATES.pop(state, None)

def create_access_token(payload: Dict[str, Any]) -> str:
    """Create JWT access token"""
    import jwt
    from shared.config import get_jwt_config

    try:
        # Get JWT configuration
        jwt_config = get_jwt_config()

        # Debug: Check if jwt_config is actually a dict
        if not isinstance(jwt_config, dict):
            logger.error(f"jwt_config is not a dict, it's a {type(jwt_config)}: {jwt_config}")
            raise ValueError(f"JWT config should be a dict, got {type(jwt_config)}")

        # Add expiration
        payload['exp'] = datetime.now(timezone.utc) + timedelta(minutes=jwt_config.get('access_token_expire_minutes', 30))

        return jwt.encode(payload, jwt_config.get('secret', 'fallback_secret'), algorithm=jwt_config.get('algorithm', 'HS256'))
    except Exception as e:
        logger.error(f"Error creating access token: {str(e)}")
        raise

def create_refresh_token() -> str:
    """Create refresh token"""
    return secrets.token_hex(32)

# Email/password login endpoint removed - Azure AD authentication only

@bp.route(route="auth/azure/login", methods=["GET"])
def azure_login(req: func.HttpRequest) -> func.HttpResponse:
    """Azure AD login endpoint"""
    logger.info('Processing Azure AD login request...')
    
    try:
        # Clean up expired states
        cleanup_expired_states()
        
        # Get redirect_uri from query parameters or use default
        redirect_uri = req.params.get("redirect_uri", "/")
        
        # Generate state parameter for CSRF protection
        state = secrets.token_hex(16)
        expires_at = datetime.now(timezone.utc) + timedelta(minutes=STATE_TIMEOUT_MINUTES)
        
        # Store state with redirect URI
        STATES[state] = {
            "redirect_uri": redirect_uri,
            "expires_at": expires_at
        }
        
        # Get Azure AD configuration
        azure_config = get_azure_ad_config()
        
        # Build authorization URL
        auth_url = (
            f"{azure_config['authority']}/oauth2/v2.0/authorize"
            f"?client_id={azure_config['client_id']}"
            f"&response_type=code"
            f"&redirect_uri={urllib.parse.quote(azure_config['redirect_uri'])}"
            f"&scope={urllib.parse.quote(azure_config['scope'])}"
            f"&state={state}"
            f"&response_mode=query"
            f"&prompt=select_account"
        )
        
        logger.info(f"Redirecting to Azure AD login URL: {auth_url}")
        
        # Redirect to Azure AD login
        response = func.HttpResponse(status_code=302)
        response.headers['Location'] = auth_url

        return add_cors_headers(req, response)
        
    except Exception as e:
        logger.error(f"Error in azure_login: {str(e)}")
        response = func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )
        return add_cors_headers(req, response)

@bp.route(route="auth/azure/callback", methods=["GET"])
def azure_callback(req: func.HttpRequest) -> func.HttpResponse:
    """Azure AD callback endpoint"""
    logger.info('Processing Azure AD callback request...')
    
    try:
        # Get authorization code and state from query parameters
        code = req.params.get("code")
        state = req.params.get("state")
        error = req.params.get("error")
        error_description = req.params.get("error_description")
        
        # Check for errors
        if error:
            logger.error(f"Azure AD authentication error: {error} - {error_description}")
            return func.HttpResponse(
                f"<html><body><h1>Authentication Error</h1><p>{error}: {error_description}</p></body></html>",
                mimetype="text/html",
                status_code=400
            )
        
        # Validate state parameter
        if not state or state not in STATES:
            logger.error(f"Invalid or expired state parameter: {state}")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Invalid or expired state parameter"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Get redirect URI from state
        redirect_uri = STATES[state].get("redirect_uri", "/")
        
        # Remove state from storage
        STATES.pop(state, None)
        
        # Check for authorization code
        if not code:
            logger.error("No authorization code received")
            return func.HttpResponse(
                "<html><body><h1>Authentication Error</h1><p>No authorization code received</p></body></html>",
                mimetype="text/html",
                status_code=400
            )
        
        # Exchange authorization code for tokens
        azure_config = get_azure_ad_config()
        token_url = f"{azure_config['authority']}/oauth2/v2.0/token"
        
        token_data = {
            "client_id": azure_config["client_id"],
            "client_secret": azure_config["client_secret"],
            "code": code,
            "redirect_uri": azure_config["redirect_uri"],
            "grant_type": "authorization_code"
        }
        
        # Make the token request
        logger.info(f"Making token request to: {token_url}")
        token_response = requests.post(token_url, data=token_data)
        
        if token_response.status_code != 200:
            error_text = token_response.text
            logger.error(f"Token exchange failed: {error_text}")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Token exchange failed", "details": error_text}),
                mimetype="application/json",
                status_code=500
            )
        
        # Parse token response
        token_data = token_response.json()
        access_token = token_data.get("access_token")
        id_token = token_data.get("id_token")
        
        if not access_token or not id_token:
            logger.error("No tokens received")
            return func.HttpResponse(
                "<html><body><h1>Authentication Error</h1><p>No tokens received</p></body></html>",
                mimetype="text/html",
                status_code=500
            )
        
        # Get user info from Microsoft Graph API
        graph_url = "https://graph.microsoft.com/v1.0/me"
        headers = {"Authorization": f"Bearer {access_token}"}
        
        graph_response = requests.get(graph_url, headers=headers)
        
        if graph_response.status_code != 200:
            logger.error(f"Graph API request failed: {graph_response.text}")
            return func.HttpResponse(
                "<html><body><h1>Authentication Error</h1><p>Failed to get user information</p></body></html>",
                mimetype="text/html",
                status_code=500
            )
        
        # Parse user info
        user_info = graph_response.json()
        email = user_info.get("userPrincipalName") or user_info.get("mail")
        name = user_info.get("displayName")
        
        if not email:
            logger.error("No email found in user info")
            return func.HttpResponse(
                "<html><body><h1>Authentication Error</h1><p>No email found in user information</p></body></html>",
                mimetype="text/html",
                status_code=500
            )
        
        # Create application access token
        app_access_token = create_access_token({"sub": email})
        
        # Create refresh token
        refresh_token = create_refresh_token()
        
        logger.info(f"Successfully authenticated user: {email}")
        
        # Get frontend URL
        frontend_url = os.environ.get('FRONTEND_URL', 'http://localhost:3000')
        
        # Create redirect URL with tokens
        redirect_url = f"{frontend_url}/auth-callback?access_token={app_access_token}&refresh_token={refresh_token}&email={urllib.parse.quote(email)}&name={urllib.parse.quote(name if name else '')}"
        
        logger.info(f"Redirecting to frontend: {redirect_url}")
        
        response = func.HttpResponse(status_code=302)
        response.headers['Location'] = redirect_url
        
        return response
        
    except Exception as e:
        logger.error(f"Error in azure_callback: {str(e)}")
        return func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="auth/azure/me", methods=["GET"])
def azure_me(req: func.HttpRequest) -> func.HttpResponse:
    """Get current user information"""
    logger.info('Processing Azure AD me request...')
    
    try:
        # Get authorization header
        auth_header = req.headers.get("Authorization")
        
        if not auth_header or not auth_header.startswith("Bearer "):
            logger.warning("No authorization header found")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Extract token
        token = auth_header.replace("Bearer ", "")
        
        # Validate token
        try:
            import jwt
            from shared.config import get_jwt_config

            jwt_config = get_jwt_config()
            payload = jwt.decode(token, jwt_config['secret'], algorithms=[jwt_config['algorithm']])
            
            email = payload.get("sub")
            
            if not email:
                logger.warning("No email found in token")
                return func.HttpResponse(
                    json.dumps({"success": False, "error": "Invalid token"}),
                    mimetype="application/json",
                    status_code=401
                )
            
            # Get actual user data from database
            try:
                from repositories.user_repository import UserRepository
                user_repo = UserRepository()
                user_data = user_repo.get_user_by_email(email)

                if user_data:
                    response_data = {
                        "success": True,
                        "data": {
                            "email": email,
                            "user_id": str(user_data.get('user_id', '')),  # Real database ID
                            "name": user_data.get('name', ''),
                            "first_name": user_data.get('first_name', ''),
                            "last_name": user_data.get('last_name', ''),
                            "roles": ["user"],  # Can be enhanced based on user data
                            "isAdmin": False  # Can be enhanced based on user data
                        }
                    }
                else:
                    # User not found in database
                    response_data = {
                        "success": False,
                        "error": "User not found in database"
                    }
            except Exception as db_error:
                logger.error(f"Error getting user data from database: {str(db_error)}")
                # Fallback to basic user info
                response_data = {
                    "success": True,
                    "data": {
                        "email": email,
                        "user_id": "",  # Empty if database lookup fails
                        "name": "",
                        "roles": ["user"],
                        "isAdmin": False
                    }
                }
            
            response = func.HttpResponse(
                json.dumps(response_data),
                mimetype="application/json",
                status_code=200
            )
            return add_cors_headers(req, response)
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Token expired"}),
                mimetype="application/json",
                status_code=401
            )
        except jwt.InvalidTokenError:
            logger.warning("Invalid token")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Invalid token"}),
                mimetype="application/json",
                status_code=401
            )
        
    except Exception as e:
        logger.error(f"Error in azure_me: {str(e)}")
        return func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="auth/token/refresh", methods=["POST"])
def token_refresh(req: func.HttpRequest) -> func.HttpResponse:
    """Refresh JWT token endpoint"""
    logger.info('Processing token refresh request...')

    try:
        # Get request body
        try:
            body = req.get_json()
        except ValueError:
            logger.warning("Invalid JSON in request body")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Invalid JSON"}),
                mimetype="application/json",
                status_code=400
            )

        if not body:
            logger.warning("No request body provided")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Request body required"}),
                mimetype="application/json",
                status_code=400
            )

        # Get refresh token from request
        refresh_token = body.get("refresh_token")

        if not refresh_token:
            logger.warning("No refresh token provided")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Refresh token required"}),
                mimetype="application/json",
                status_code=400
            )

        # Get current access token to extract user info
        auth_header = req.headers.get("Authorization")
        current_token = None

        if auth_header and auth_header.startswith("Bearer "):
            current_token = auth_header.replace("Bearer ", "")

        # Try to decode current token to get user info (even if expired)
        user_email = None
        if current_token:
            try:
                import jwt
                from shared.config import get_jwt_config

                jwt_config = get_jwt_config()
                # Decode without verification to get user info from expired token
                payload = jwt.decode(current_token, jwt_config['secret'], algorithms=[jwt_config['algorithm']], options={"verify_exp": False})
                user_email = payload.get("sub")
            except Exception as e:
                logger.warning(f"Could not decode current token: {str(e)}")

        if not user_email:
            logger.warning("Could not extract user email from token")
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Invalid token"}),
                mimetype="application/json",
                status_code=401
            )

        # Create new access token
        new_access_token = create_access_token({"sub": user_email})

        # Create new refresh token
        new_refresh_token = create_refresh_token()

        logger.info(f"Successfully refreshed token for user: {user_email}")

        response_data = {
            "success": True,
            "data": {
                "access_token": new_access_token,
                "refresh_token": new_refresh_token,
                "token_type": "bearer"
            }
        }

        return func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error in token_refresh: {str(e)}")
        return func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )
