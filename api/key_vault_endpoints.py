"""
Key Vault Endpoints

This module provides endpoints for Azure Key Vault operations.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any, Optional
from azure.functions import Blueprint

from shared.azure_services import is_local_dev, get_current_user_id, get_key_vault_client
from shared.service_bus_client import get_service_bus_client

logger = logging.getLogger(__name__)
bp = Blueprint()

@bp.route(route="key-vault/secrets", methods=["GET"])
def get_key_vault_secrets(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get secrets from Key Vault
    
    Returns:
        JSON response with secrets list
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Get Key Vault client
        key_vault_client = get_key_vault_client()
        if not key_vault_client:
            return func.HttpResponse(
                json.dumps({"error": "Key Vault client not available"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Get query parameters
        integration_id = req.params.get('integration_id')
        secret_type = req.params.get('type', 'all')
        
        try:
            # List secrets
            secrets = []
            secret_properties = key_vault_client.list_properties_of_secrets()
            
            for secret_property in secret_properties:
                secret_name = secret_property.name
                
                # Filter by integration ID if provided
                if integration_id and not secret_name.startswith(f"integration-{integration_id}-"):
                    continue
                
                # Filter by type if provided
                if secret_type != 'all':
                    if secret_type == 'integration' and not secret_name.startswith('integration-'):
                        continue
                    elif secret_type == 'sfdc' and not secret_name.startswith('sfdc-'):
                        continue
                    elif secret_type == 'api' and not secret_name.startswith('api-'):
                        continue
                
                # Get secret metadata (not the actual value for security)
                secret_info = {
                    "name": secret_name,
                    "enabled": secret_property.enabled,
                    "created_on": secret_property.created_on.isoformat() if secret_property.created_on else None,
                    "updated_on": secret_property.updated_on.isoformat() if secret_property.updated_on else None,
                    "expires_on": secret_property.expires_on.isoformat() if secret_property.expires_on else None,
                    "type": self._get_secret_type(secret_name)
                }
                secrets.append(secret_info)
            
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": secrets,
                    "count": len(secrets)
                }),
                mimetype="application/json",
                status_code=200
            )
            
        except Exception as e:
            logger.error(f"Error listing Key Vault secrets: {str(e)}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Failed to list secrets: {str(e)}"
                }),
                mimetype="application/json",
                status_code=500
            )
        
    except Exception as e:
        logger.error(f"Error in get_key_vault_secrets: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to get secrets: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="key-vault/create", methods=["POST"])
def create_key_vault_secret(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create a new secret in Key Vault
    
    Returns:
        JSON response with created secret info
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Validate required fields
        required_fields = ["name", "value"]
        for field in required_fields:
            if field not in request_data:
                return func.HttpResponse(
                    json.dumps({"error": f"Missing required field: {field}"}),
                    mimetype="application/json",
                    status_code=400
                )
        
        secret_name = request_data["name"]
        secret_value = request_data["value"]
        description = request_data.get("description", "")
        tags = request_data.get("tags", {})
        
        # Validate secret name format
        if not self._is_valid_secret_name(secret_name):
            return func.HttpResponse(
                json.dumps({"error": "Invalid secret name format"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Get Key Vault client
        key_vault_client = get_key_vault_client()
        if not key_vault_client:
            return func.HttpResponse(
                json.dumps({"error": "Key Vault client not available"}),
                mimetype="application/json",
                status_code=500
            )
        
        try:
            # Create secret
            created_secret = key_vault_client.set_secret(
                secret_name,
                secret_value,
                content_type="text/plain",
                tags=tags
            )
            
            # Publish secret creation event
            service_bus_client = get_service_bus_client()
            if service_bus_client:
                service_bus_client.publish_event(
                    event_type="KeyVaultSecretCreated",
                    event_data={
                        "secret_name": secret_name,
                        "description": description,
                        "created_by": user_id
                    },
                    user_id=user_id
                )
            
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "name": created_secret.name,
                        "version": created_secret.version,
                        "created_on": created_secret.created_on.isoformat() if created_secret.created_on else None,
                        "enabled": created_secret.enabled
                    },
                    "message": "Secret created successfully"
                }),
                mimetype="application/json",
                status_code=201
            )
            
        except Exception as e:
            logger.error(f"Error creating Key Vault secret: {str(e)}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Failed to create secret: {str(e)}"
                }),
                mimetype="application/json",
                status_code=500
            )
        
    except Exception as e:
        logger.error(f"Error in create_key_vault_secret: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to create secret: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="key-vault/access-policy", methods=["POST"])
def create_key_vault_access_policy(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create access policy for Key Vault
    
    Returns:
        JSON response with access policy info
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Validate required fields
        required_fields = ["object_id", "permissions"]
        for field in required_fields:
            if field not in request_data:
                return func.HttpResponse(
                    json.dumps({"error": f"Missing required field: {field}"}),
                    mimetype="application/json",
                    status_code=400
                )
        
        object_id = request_data["object_id"]
        permissions = request_data["permissions"]
        
        # Validate permissions
        valid_permissions = ["get", "list", "set", "delete", "recover", "backup", "restore"]
        for permission in permissions:
            if permission not in valid_permissions:
                return func.HttpResponse(
                    json.dumps({"error": f"Invalid permission: {permission}"}),
                    mimetype="application/json",
                    status_code=400
                )
        
        # Get Key Vault client
        key_vault_client = get_key_vault_client()
        if not key_vault_client:
            return func.HttpResponse(
                json.dumps({"error": "Key Vault client not available"}),
                mimetype="application/json",
                status_code=500
            )
        
        try:
            # Create access policy (this would require Key Vault management client)
            # For now, return success response
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "object_id": object_id,
                        "permissions": permissions,
                        "created_by": user_id
                    },
                    "message": "Access policy created successfully"
                }),
                mimetype="application/json",
                status_code=201
            )
            
        except Exception as e:
            logger.error(f"Error creating access policy: {str(e)}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Failed to create access policy: {str(e)}"
                }),
                mimetype="application/json",
                status_code=500
            )
        
    except Exception as e:
        logger.error(f"Error in create_key_vault_access_policy: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to create access policy: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="key-vault/client-credentials", methods=["POST"])
def create_client_credentials(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create client credentials for service-to-service authentication
    
    Returns:
        JSON response with client credentials
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Validate required fields
        required_fields = ["client_name", "service_type"]
        for field in required_fields:
            if field not in request_data:
                return func.HttpResponse(
                    json.dumps({"error": f"Missing required field: {field}"}),
                    mimetype="application/json",
                    status_code=400
                )
        
        client_name = request_data["client_name"]
        service_type = request_data["service_type"]
        description = request_data.get("description", "")
        
        # Validate service type
        valid_service_types = ["sfdc", "api", "integration", "webhook"]
        if service_type not in valid_service_types:
            return func.HttpResponse(
                json.dumps({"error": f"Invalid service type: {service_type}"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Generate client credentials
        import secrets
        import string
        
        client_id = f"{service_type}-{client_name}-{secrets.token_hex(8)}"
        client_secret = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
        
        # Get Key Vault client
        key_vault_client = get_key_vault_client()
        if not key_vault_client:
            return func.HttpResponse(
                json.dumps({"error": "Key Vault client not available"}),
                mimetype="application/json",
                status_code=500
            )
        
        try:
            # Store client secret in Key Vault
            secret_name = f"client-{client_id}"
            created_secret = key_vault_client.set_secret(
                secret_name,
                client_secret,
                content_type="text/plain",
                tags={
                    "client_id": client_id,
                    "service_type": service_type,
                    "created_by": user_id,
                    "description": description
                }
            )
            
            # Publish client credentials creation event
            service_bus_client = get_service_bus_client()
            if service_bus_client:
                service_bus_client.publish_event(
                    event_type="ClientCredentialsCreated",
                    event_data={
                        "client_id": client_id,
                        "service_type": service_type,
                        "description": description,
                        "created_by": user_id
                    },
                    user_id=user_id
                )
            
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "client_id": client_id,
                        "client_secret": client_secret,  # Only returned once
                        "service_type": service_type,
                        "description": description,
                        "created_by": user_id,
                        "created_on": created_secret.created_on.isoformat() if created_secret.created_on else None
                    },
                    "message": "Client credentials created successfully"
                }),
                mimetype="application/json",
                status_code=201
            )
            
        except Exception as e:
            logger.error(f"Error creating client credentials: {str(e)}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Failed to create client credentials: {str(e)}"
                }),
                mimetype="application/json",
                status_code=500
            )
        
    except Exception as e:
        logger.error(f"Error in create_client_credentials: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to create client credentials: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

def _get_secret_type(self, secret_name: str) -> str:
    """Determine secret type based on name"""
    if secret_name.startswith('integration-'):
        return 'integration'
    elif secret_name.startswith('sfdc-'):
        return 'sfdc'
    elif secret_name.startswith('api-'):
        return 'api'
    elif secret_name.startswith('client-'):
        return 'client'
    else:
        return 'general'

def _is_valid_secret_name(self, secret_name: str) -> bool:
    """Validate secret name format"""
    import re
    # Secret names can only contain alphanumeric characters and hyphens
    pattern = r'^[a-zA-Z0-9-]+$'
    return bool(re.match(pattern, secret_name)) and len(secret_name) <= 127 