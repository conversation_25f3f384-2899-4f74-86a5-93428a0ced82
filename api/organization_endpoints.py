"""
Organization Management Endpoints

This module provides organization-related database operations for the AtomSec application.
Moved from atomsec-func-sfdc to centralize database operations.

Endpoints:
- GET /api/organizations - List organizations with optional filtering
- POST /api/organizations - Create new organization
- GET /api/organizations/{id} - Get organization by ID
- PUT /api/organizations/{id} - Update organization
- DELETE /api/organizations/{id} - Delete organization
- GET /api/organizations/{id}/integrations - Get organization integrations
"""

import logging
import json
import uuid
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models_new import Organization

logger = logging.getLogger(__name__)

# Global repository instances (lazy initialized)
_organization_table_repo = None
_organization_sql_repo = None


def get_organization_table_repo() -> Optional[TableStorageRepository]:
    """Get organization table repository for local development"""
    global _organization_table_repo
    if _organization_table_repo is None:
        try:
            _organization_table_repo = TableStorageRepository(table_name="Organizations")
            logger.info("Initialized organization table repository")
        except Exception as e:
            logger.error(f"Failed to initialize organization table repository: {str(e)}")
            _organization_table_repo = None
    return _organization_table_repo


def get_organization_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get organization SQL repository for production"""
    global _organization_sql_repo
    if _organization_sql_repo is None and not is_local_dev():
        try:
            _organization_sql_repo = SqlDatabaseRepository(table_name="App_Organization")
            logger.info("Initialized organization SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize organization SQL repository: {str(e)}")
            _organization_sql_repo = None
    return _organization_sql_repo


def create_organization_record(name: str, description: str = "", domain: str = "",
                              contact_email: str = "", is_active: bool = True) -> Optional[str]:
    """
    Create a new organization record

    Args:
        name: Organization name
        description: Organization description
        domain: Organization domain
        contact_email: Contact email
        is_active: Whether the organization is active

    Returns:
        Organization ID if successful, None otherwise
    """
    try:
        # Generate unique organization ID
        org_id = str(uuid.uuid4())

        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_organization_table_repo()
            if not repo:
                logger.error("Organization table repository not available")
                return None

            # Create organization entity
            organization_entity = {
                "PartitionKey": "organization",
                "RowKey": org_id,
                "Name": name,
                "Description": description,
                "Domain": domain,
                "ContactEmail": contact_email,
                "IsActive": is_active,
                "CreatedAt": datetime.now().isoformat(),
                "UpdatedAt": datetime.now().isoformat()
            }

            if repo.insert_entity(organization_entity):
                logger.info(f"Created organization: {name} with ID {org_id}")
                return org_id
            else:
                logger.error("Failed to insert organization entity")
                return None

        else:
            # Use SQL Database for production
            repo = get_organization_sql_repo()
            if not repo:
                logger.error("Organization SQL repository not available")
                return None

            query = """
            INSERT INTO App_Organization (Id, Name, Description, Domain, ContactEmail,
                                        IsActive, CreatedAt, UpdatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                org_id,
                name,
                description,
                domain,
                contact_email,
                is_active,
                datetime.now(),
                datetime.now()
            )

            if repo.execute_non_query(query, params):
                logger.info(f"Created organization: {name} with ID {org_id}")
                return org_id
            else:
                logger.error("Failed to create organization in SQL database")
                return None

    except Exception as e:
        logger.error(f"Error creating organization: {str(e)}")
        return None


def get_organization_by_id_db(org_id: str) -> Optional[Dict[str, Any]]:
    """
    Get organization by ID from database

    Args:
        org_id: Organization ID

    Returns:
        Organization data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_organization_table_repo()
            if not repo:
                logger.error("Organization table repository not available")
                return None

            organization = repo.get_entity("organization", org_id)

            if organization:
                return {
                    'id': organization.get('RowKey'),
                    'name': organization.get('Name'),
                    'description': organization.get('Description'),
                    'domain': organization.get('Domain'),
                    'contact_email': organization.get('ContactEmail'),
                    'is_active': organization.get('IsActive'),
                    'created_at': organization.get('CreatedAt'),
                    'updated_at': organization.get('UpdatedAt')
                }
            else:
                logger.warning(f"Organization not found: {org_id}")
                return None

        else:
            # Use SQL Database for production
            repo = get_organization_sql_repo()
            if not repo:
                logger.error("Organization SQL repository not available")
                return None

            query = """
            SELECT Id, Name, Description, Domain, ContactEmail, IsActive, CreatedAt, UpdatedAt
            FROM App_Organization
            WHERE Id = ?
            """

            results = repo.execute_query(query, (org_id,))

            if results:
                row = results[0]
                return {
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'domain': row[3],
                    'contact_email': row[4],
                    'is_active': row[5],
                    'created_at': row[6].isoformat() if row[6] else None,
                    'updated_at': row[7].isoformat() if row[7] else None
                }
            else:
                logger.warning(f"Organization not found: {org_id}")
                return None

    except Exception as e:
        logger.error(f"Error getting organization by ID: {str(e)}")
        return None


def get_organizations_list(include_inactive: bool = False, domain: str = None) -> List[Dict[str, Any]]:
    """
    Get organizations with optional filtering

    Args:
        include_inactive: Include inactive organizations
        domain: Filter by domain

    Returns:
        List of organization dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_organization_table_repo()
            if not repo:
                logger.error("Organization table repository not available")
                return []

            filter_parts = ["PartitionKey eq 'organization'"]

            # Apply filters
            if not include_inactive:
                filter_parts.append("IsActive eq true")
            if domain:
                filter_parts.append(f"Domain eq '{domain}'")

            filter_query = " and ".join(filter_parts)
            entities = list(repo.query_entities(filter_query))

            organizations = []
            for entity in entities:
                organizations.append({
                    'id': entity.get('RowKey'),
                    'name': entity.get('Name'),
                    'description': entity.get('Description'),
                    'domain': entity.get('Domain'),
                    'contact_email': entity.get('ContactEmail'),
                    'is_active': entity.get('IsActive'),
                    'created_at': entity.get('CreatedAt'),
                    'updated_at': entity.get('UpdatedAt')
                })

            return organizations

        else:
            # Use SQL Database for production
            repo = get_organization_sql_repo()
            if not repo:
                logger.error("Organization SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT Id, Name, Description, Domain, ContactEmail, IsActive, CreatedAt, UpdatedAt
            FROM App_Organization WHERE 1=1
            """
            params = []

            if not include_inactive:
                query += " AND IsActive = ?"
                params.append(True)
            if domain:
                query += " AND Domain = ?"
                params.append(domain)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))

            organizations = []
            for row in results:
                organizations.append({
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'domain': row[3],
                    'contact_email': row[4],
                    'is_active': row[5],
                    'created_at': row[6].isoformat() if row[6] else None,
                    'updated_at': row[7].isoformat() if row[7] else None
                })

            return organizations

    except Exception as e:
        logger.error(f"Error getting organizations: {str(e)}")
        return []


# Create blueprint
bp = func.Blueprint()

@bp.route(route="organizations", methods=["GET"])
def list_organizations(req: func.HttpRequest) -> func.HttpResponse:
    """List organizations with optional filtering"""
    try:
        # Get query parameters
        include_inactive = req.params.get('include_inactive', 'false').lower() == 'true'
        domain = req.params.get('domain')

        organizations = get_organizations_list(
            include_inactive=include_inactive,
            domain=domain
        )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {"organizations": organizations, "count": len(organizations)}
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error listing organizations: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )