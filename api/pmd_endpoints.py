"""
PMD API Endpoints

This module provides HTTP endpoints for PMD-related database operations.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any, List

from repositories.pmd_repository import PMDRepository

logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Initialize repository
pmd_repo = PMDRepository()


@bp.route(route="pmd/subtasks/{integration_id}", methods=["GET"])
def get_enabled_pmd_subtasks(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get enabled PMD subtasks for a specific integration
    
    Path Parameters:
    - integration_id: Integration ID
    
    Returns:
        JSON array of enabled PMD subtasks
    """
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        subtasks = pmd_repo.get_enabled_subtasks_for_integration(integration_id)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": subtasks,
                "count": len(subtasks)
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting PMD subtasks: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="pmd/rules/{subtask_id}", methods=["GET"])
def get_enabled_pmd_rules(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get enabled individual PMD rules for a specific subtask
    
    Path Parameters:
    - subtask_id: Subtask ID
    
    Returns:
        JSON array of enabled individual PMD rules
    """
    try:
        subtask_id = req.route_params.get('subtask_id')
        if not subtask_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Subtask ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        rules = pmd_repo.get_enabled_rules_for_subtask(subtask_id)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": rules,
                "count": len(rules)
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting PMD rules: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="pmd/configuration/{integration_id}", methods=["GET"])
def get_pmd_configuration(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get complete PMD configuration for a specific integration
    
    Path Parameters:
    - integration_id: Integration ID
    
    Returns:
        JSON object with complete PMD configuration
    """
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        configuration = pmd_repo.get_complete_configuration_for_integration(integration_id)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": configuration
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting PMD configuration: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="pmd/findings", methods=["POST"])
def store_pmd_findings(req: func.HttpRequest) -> func.HttpResponse:
    """
    Store PMD scan findings
    
    Request Body:
    {
        "findings": [
            {
                "integration_id": "integration_123",
                "task_id": "task_456",
                "execution_log_id": "exec_789",
                "blob_prefix": "org_123/metadata",
                "file_name": "MyClass.cls",
                "file_path": "force-app/main/default/classes/MyClass.cls",
                "line_number": 25,
                "rule_name": "ApexCRUDViolation",
                "rule_set": "apex-security",
                "severity": "High",
                "priority": 1,
                "category": "Security",
                "description": "CRUD permission violation detected",
                "package": "MyPackage",
                "class": "MyClass",
                "method": "myMethod"
            }
        ]
    }
    
    Returns:
        JSON object with processing results
    """
    try:
        req_body = req.get_json()
        if not req_body or 'findings' not in req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body must contain 'findings' array"
                }),
                mimetype="application/json",
                status_code=400
            )

        findings = req_body['findings']
        if not isinstance(findings, list):
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Findings must be an array"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Store findings
        result = pmd_repo.store_findings(findings)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": result
            }),
            mimetype="application/json",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Error storing PMD findings: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="pmd/findings/{integration_id}", methods=["GET"])
def get_pmd_findings(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get PMD findings for a specific integration
    
    Path Parameters:
    - integration_id: Integration ID
    
    Query Parameters:
    - limit: Maximum number of findings to return (default: 100)
    - offset: Number of findings to skip (default: 0)
    - severity: Filter by severity (High, Medium, Low)
    - category: Filter by category
    
    Returns:
        JSON object with PMD findings
    """
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Integration ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get query parameters
        limit = int(req.params.get('limit', 100))
        offset = int(req.params.get('offset', 0))
        severity = req.params.get('severity')
        category = req.params.get('category')

        findings = pmd_repo.get_findings_for_integration(
            integration_id, 
            limit=limit, 
            offset=offset,
            severity=severity,
            category=category
        )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": findings
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting PMD findings: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="pmd/subtasks/{subtask_id}/toggle", methods=["PUT"])
def toggle_pmd_subtask(req: func.HttpRequest) -> func.HttpResponse:
    """
    Toggle enabled status of a PMD subtask
    
    Path Parameters:
    - subtask_id: Subtask ID
    
    Body:
        JSON object with enabled status
        {
            "enabled": true/false
        }
    
    Returns:
        JSON object with updated subtask
    """
    try:
        subtask_id = req.route_params.get('subtask_id')
        if not subtask_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Subtask ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        req_body = req.get_json()
        if not req_body or 'enabled' not in req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body must contain 'enabled' field"
                }),
                mimetype="application/json",
                status_code=400
            )

        enabled = bool(req_body['enabled'])
        
        # Update subtask enabled status
        success = pmd_repo.update_subtask_enabled_status(subtask_id, enabled)
        
        if not success:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to update subtask status"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": f"Subtask {'enabled' if enabled else 'disabled'} successfully"
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error toggling PMD subtask: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="pmd/rules/{rule_id}/toggle", methods=["PUT"])
def toggle_pmd_rule(req: func.HttpRequest) -> func.HttpResponse:
    """
    Toggle enabled status of an individual PMD rule
    
    Path Parameters:
    - rule_id: Individual rule ID
    
    Body:
        JSON object with enabled status
        {
            "enabled": true/false
        }
    
    Returns:
        JSON object with updated rule
    """
    try:
        rule_id = req.route_params.get('rule_id')
        if not rule_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Rule ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        req_body = req.get_json()
        if not req_body or 'enabled' not in req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body must contain 'enabled' field"
                }),
                mimetype="application/json",
                status_code=400
            )

        enabled = bool(req_body['enabled'])
        
        # Update rule enabled status
        success = pmd_repo.update_rule_enabled_status(rule_id, enabled)
        
        if not success:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to update rule status"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": f"Rule {'enabled' if enabled else 'disabled'} successfully"
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error toggling PMD rule: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        ) 