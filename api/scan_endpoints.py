"""
Scan Endpoints

This module provides HTTP endpoints for scan initiation that match the dev branch exactly.
It handles scan requests and coordinates with the background processor for task execution.

Endpoints:
- POST /api/scan/initiate - Initiate a complete scan
- POST /api/scan/policy - Initiate a policy-based scan
- GET /api/scan/status/{scan_id} - Get scan status
"""

import logging
import json
import azure.functions as func
from datetime import datetime
from typing import Dict, Any, Optional

# Import shared modules
from shared.scan_initiation import get_scan_initiation_service
from shared.background_processor import (
    TASK_TYPE_OVERVIEW,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_PROFILES,
    TASK_TYPE_PMD_APEX_SECURITY,
    TASK_PRIORITY_HIGH,
    TASK_PRIORITY_MEDIUM,
    TASK_PRIORITY_LOW
)
from shared.execution_log_service import get_execution_log_service
from shared.cors_middleware import add_cors_headers

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()


@bp.route(route="scan/initiate", methods=["POST"])
def initiate_scan(req: func.HttpRequest) -> func.HttpResponse:
    """
    Initiate a complete scan workflow
    
    Expected request body:
    {
        "integration_id": "string",
        "user_id": "string", 
        "scan_types": ["overview", "health_check", "profiles", "pmd_apex_security"],
        "params": {
            "access_token": "string",
            "instance_url": "string",
            "environment": "production",
            "org_name": "string",
            "blob_prefix": "string"
        },
        "priority": "medium"
    }
    """
    try:
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )
        
        if not request_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )
        
        # Validate required fields
        required_fields = ["integration_id", "user_id", "scan_types"]
        for field in required_fields:
            if field not in request_data:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Missing required field: {field}"
                    }),
                    mimetype="application/json",
                    status_code=400,
                    headers=add_cors_headers({})
                )
        
        integration_id = request_data["integration_id"]
        user_id = request_data["user_id"]
        scan_types = request_data["scan_types"]
        params = request_data.get("params", {})
        priority = request_data.get("priority", TASK_PRIORITY_MEDIUM)
        
        # Validate scan types
        valid_scan_types = ["overview", "health_check", "profiles", "profiles_permission_sets", "permission_sets", "pmd_apex_security"]
        invalid_types = [st for st in scan_types if st not in valid_scan_types]
        if invalid_types:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Invalid scan types: {invalid_types}. Valid types: {valid_scan_types}"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )
        
        # Validate priority
        valid_priorities = [TASK_PRIORITY_HIGH, TASK_PRIORITY_MEDIUM, TASK_PRIORITY_LOW]
        if priority not in valid_priorities:
            priority = TASK_PRIORITY_MEDIUM
        
        logger.info(f"Initiating scan for integration {integration_id} with types: {scan_types}")
        
        # Get scan initiation service
        scan_service = get_scan_initiation_service()
        
        # Initiate the scan
        result = scan_service.initiate_complete_scan(
            integration_id=integration_id,
            user_id=user_id,
            scan_types=scan_types,
            params=params,
            priority=priority
        )
        
        status_code = 200 if result.get("success") else 500
        
        return func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code,
            headers=add_cors_headers({})
        )
        
    except Exception as e:
        logger.error(f"Error in scan initiation endpoint: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500,
            headers=add_cors_headers({})
        )


@bp.route(route="scan/policy", methods=["POST"])
def initiate_policy_scan(req: func.HttpRequest) -> func.HttpResponse:
    """
    Initiate a policy-based scan
    
    Expected request body:
    {
        "integration_id": "string",
        "user_id": "string",
        "policy_name": "string",
        "params": {
            "access_token": "string",
            "instance_url": "string",
            "environment": "production"
        },
        "priority": "medium"
    }
    """
    try:
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )
        
        if not request_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )
        
        # Validate required fields
        required_fields = ["integration_id", "user_id", "policy_name"]
        for field in required_fields:
            if field not in request_data:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Missing required field: {field}"
                    }),
                    mimetype="application/json",
                    status_code=400,
                    headers=add_cors_headers({})
                )
        
        integration_id = request_data["integration_id"]
        user_id = request_data["user_id"]
        policy_name = request_data["policy_name"]
        params = request_data.get("params", {})
        priority = request_data.get("priority", TASK_PRIORITY_MEDIUM)
        
        logger.info(f"Initiating policy-based scan for policy '{policy_name}' on integration {integration_id}")
        
        # Get scan initiation service
        scan_service = get_scan_initiation_service()
        
        # Initiate the policy-based scan
        result = scan_service.initiate_policy_based_scan(
            integration_id=integration_id,
            user_id=user_id,
            policy_name=policy_name,
            params=params,
            priority=priority
        )
        
        status_code = 200 if result.get("success") else 500
        
        return func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code,
            headers=add_cors_headers({})
        )
        
    except Exception as e:
        logger.error(f"Error in policy scan initiation endpoint: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500,
            headers=add_cors_headers({})
        )


@bp.route(route="scan/status/{scan_id}", methods=["GET"])
def get_scan_status(req: func.HttpRequest) -> func.HttpResponse:
    """Get scan status by scan ID"""
    try:
        scan_id = req.route_params.get('scan_id')
        if not scan_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Scan ID is required"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )
        
        # Get execution log service
        execution_service = get_execution_log_service()
        
        # Get scan status from execution log
        execution_log = execution_service.get_execution_log(scan_id)
        
        if execution_log:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "scan_id": scan_id,
                        "status": execution_log.get("Status"),
                        "execution_type": execution_log.get("ExecutionType"),
                        "start_time": execution_log.get("StartTime"),
                        "end_time": execution_log.get("EndTime"),
                        "org_id": execution_log.get("OrgId"),
                        "executed_by": execution_log.get("ExecutedBy")
                    }
                }),
                mimetype="application/json",
                status_code=200,
                headers=add_cors_headers({})
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Scan not found"
                }),
                mimetype="application/json",
                status_code=404,
                headers=add_cors_headers({})
            )
            
    except Exception as e:
        logger.error(f"Error getting scan status: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500,
            headers=add_cors_headers({})
        )
