"""
Task Processor Endpoints

This module provides HTTP endpoints for receiving and processing tasks from the SFDC service.
It acts as the entry point for task processing in the microservices architecture.

Endpoints:
- POST /api/task/high - Process high priority tasks
- POST /api/task/medium - Process medium priority tasks  
- POST /api/task/low - Process low priority tasks
- POST /api/task/process - Generic task processing endpoint
- GET /api/task/status/{task_id} - Get task status
"""

import logging
import json
import azure.functions as func
from datetime import datetime
from typing import Dict, Any, Optional

# Import shared modules
from shared.background_processor import (
    get_background_processor,
    TASK_STATUS_PENDING,
    TASK_STATUS_RUNNING,
    TASK_STATUS_COMPLETED,
    TASK_STATUS_FAILED,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_PROFILES,
    TASK_TYPE_OVERVIEW,
    TASK_TYPE_PMD_APEX_SECURITY,
    TASK_TYPE_METADATA_EXTRACTION
)
from shared.task_status_service import get_task_status_service
from shared.execution_log_service import get_execution_log_service
from shared.cors_middleware import add_cors_headers
from shared.pmd_subtask_service import get_pmd_subtask_service
from shared.policy_management import get_policy_management_service

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()


def process_task_message(task_data: Dict[str, Any], priority: str) -> Dict[str, Any]:
    """
    Process a task message received from the SFDC service
    
    Args:
        task_data: Task data from the message
        priority: Task priority level
    
    Returns:
        Dict[str, Any]: Processing result
    """
    try:
        task_id = task_data.get("task_id")
        task_type = task_data.get("task_type")
        org_id = task_data.get("org_id")
        user_id = task_data.get("user_id")
        params = task_data.get("params", {})
        execution_log_id = task_data.get("execution_log_id")
        
        logger.info(f"Processing {priority} priority task: {task_id} (type: {task_type})")
        
        # Get services
        task_service = get_task_status_service()
        execution_service = get_execution_log_service()
        
        # Update task status to running
        task_service.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=0,
            message=f"Started processing {task_type} task"
        )
        
        # Update execution log
        if execution_log_id:
            execution_service.update_execution_log_status(
                execution_log_id=execution_log_id,
                status="Running"
            )
        
        # Route task to appropriate processor
        result = route_task_to_processor(task_type, task_id, org_id, user_id, params, execution_log_id)
        
        if result.get("success"):
            # Update task status to completed
            task_service.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_COMPLETED,
                progress=100,
                message="Task completed successfully",
                result=json.dumps(result.get("data", {}))
            )
            
            # Update execution log
            if execution_log_id:
                execution_service.update_execution_log_status(
                    execution_log_id=execution_log_id,
                    status="Completed"
                )
            
            logger.info(f"Task {task_id} completed successfully")
            
        else:
            # Update task status to failed
            error_message = result.get("error", "Unknown error")
            task_service.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                message=f"Task failed: {error_message}",
                error=error_message
            )
            
            # Update execution log
            if execution_log_id:
                execution_service.update_execution_log_status(
                    execution_log_id=execution_log_id,
                    status="Failed"
                )
            
            logger.error(f"Task {task_id} failed: {error_message}")
        
        return result
        
    except Exception as e:
        error_msg = f"Error processing task: {str(e)}"
        logger.error(error_msg)
        
        # Update task status to failed if we have task_id
        task_id = task_data.get("task_id")
        if task_id:
            try:
                task_service = get_task_status_service()
                task_service.update_task_status(
                    task_id=task_id,
                    status=TASK_STATUS_FAILED,
                    message=error_msg,
                    error=str(e)
                )
            except Exception as update_error:
                logger.error(f"Failed to update task status: {str(update_error)}")
        
        return {
            "success": False,
            "error": error_msg
        }


def route_task_to_processor(
    task_type: str,
    task_id: str,
    org_id: str,
    user_id: str,
    params: Dict[str, Any],
    execution_log_id: Optional[str]
) -> Dict[str, Any]:
    """
    Route task to the appropriate processor based on task type
    
    Args:
        task_type: Type of task to process
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    
    Returns:
        Dict[str, Any]: Processing result
    """
    try:
        logger.info(f"Routing task {task_id} of type {task_type}")
        
        # For now, we'll implement basic task processing
        # In a full implementation, each task type would have its own processor
        
        if task_type == TASK_TYPE_HEALTH_CHECK:
            return process_health_check_task(task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PROFILES:
            return process_profiles_task(task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_OVERVIEW:
            return process_overview_task(task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PMD_APEX_SECURITY:
            return process_pmd_task(task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_METADATA_EXTRACTION:
            return process_metadata_extraction_task(task_id, org_id, user_id, params, execution_log_id)
        else:
            return process_generic_task(task_id, org_id, user_id, params, execution_log_id, task_type)
            
    except Exception as e:
        logger.error(f"Error routing task {task_id}: {str(e)}")
        return {
            "success": False,
            "error": f"Task routing failed: {str(e)}"
        }


def process_health_check_task(task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str]) -> Dict[str, Any]:
    """Process health check task"""
    logger.info(f"Processing health check task {task_id} for org {org_id}")
    
    # Simulate health check processing
    # In a real implementation, this would call the SFDC service to perform the health check
    
    return {
        "success": True,
        "data": {
            "task_id": task_id,
            "task_type": "health_check",
            "org_id": org_id,
            "status": "completed",
            "results": {
                "health_score": 85,
                "issues_found": 3,
                "recommendations": 5
            },
            "processed_at": datetime.now().isoformat()
        }
    }


def process_profiles_task(task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str]) -> Dict[str, Any]:
    """Process profiles analysis task"""
    logger.info(f"Processing profiles task {task_id} for org {org_id}")
    
    return {
        "success": True,
        "data": {
            "task_id": task_id,
            "task_type": "profiles",
            "org_id": org_id,
            "status": "completed",
            "results": {
                "profiles_analyzed": 15,
                "issues_found": 7,
                "high_risk_profiles": 2
            },
            "processed_at": datetime.now().isoformat()
        }
    }


def process_overview_task(task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str]) -> Dict[str, Any]:
    """Process overview task"""
    logger.info(f"Processing overview task {task_id} for org {org_id}")
    
    return {
        "success": True,
        "data": {
            "task_id": task_id,
            "task_type": "overview",
            "org_id": org_id,
            "status": "completed",
            "results": {
                "total_users": 150,
                "active_users": 120,
                "security_score": 78
            },
            "processed_at": datetime.now().isoformat()
        }
    }


def process_pmd_task(task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str]) -> Dict[str, Any]:
    """Process PMD security analysis task with subtask support"""
    logger.info(f"Processing PMD task {task_id} for org {org_id}")

    try:
        # Get PMD subtask service
        pmd_service = get_pmd_subtask_service()

        # Get enabled subtasks for this integration
        enabled_subtasks = pmd_service.get_enabled_pmd_subtasks_for_integration(org_id)

        # Get scan categories from enabled subtasks or use defaults
        scan_categories = params.get("scan_categories")
        if not scan_categories and enabled_subtasks:
            scan_categories = [subtask.get("SubtaskName", "") for subtask in enabled_subtasks if subtask.get("Enabled")]

        if not scan_categories:
            scan_categories = ["Security", "Best Practices", "Code Style"]  # Default categories

        logger.info(f"PMD task {task_id} will scan {len(scan_categories)} categories: {scan_categories}")

        # Simulate PMD processing with subtask-based scanning
        results = {
            "files_scanned": 45,
            "vulnerabilities_found": 12,
            "critical_issues": 3,
            "scan_categories": scan_categories,
            "enabled_subtasks": len(enabled_subtasks),
            "subtask_details": [
                {
                    "subtask_id": subtask.get("SubtaskId"),
                    "name": subtask.get("SubtaskName"),
                    "enabled": subtask.get("Enabled")
                }
                for subtask in enabled_subtasks
            ]
        }

        return {
            "success": True,
            "data": {
                "task_id": task_id,
                "task_type": "pmd_apex_security",
                "org_id": org_id,
                "status": "completed",
                "results": results,
                "processed_at": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Error processing PMD task {task_id}: {str(e)}")
        return {
            "success": False,
            "error": f"PMD task processing failed: {str(e)}"
        }


def process_metadata_extraction_task(task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str]) -> Dict[str, Any]:
    """Process metadata extraction task"""
    logger.info(f"Processing metadata extraction task {task_id} for org {org_id}")
    
    return {
        "success": True,
        "data": {
            "task_id": task_id,
            "task_type": "metadata_extraction",
            "org_id": org_id,
            "status": "completed",
            "results": {
                "metadata_extracted": True,
                "components_found": 234,
                "extraction_time": "45 seconds"
            },
            "processed_at": datetime.now().isoformat()
        }
    }


def process_generic_task(task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str], task_type: str) -> Dict[str, Any]:
    """Process generic task"""
    logger.info(f"Processing generic task {task_id} of type {task_type} for org {org_id}")
    
    return {
        "success": True,
        "data": {
            "task_id": task_id,
            "task_type": task_type,
            "org_id": org_id,
            "status": "completed",
            "results": {
                "message": f"Generic processing completed for {task_type}",
                "params_received": params
            },
            "processed_at": datetime.now().isoformat()
        }
    }


# HTTP Endpoints

@bp.route(route="task/high", methods=["POST"])
def process_high_priority_task(req: func.HttpRequest) -> func.HttpResponse:
    """Process high priority task"""
    try:
        # Parse request body
        try:
            task_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )

        if not task_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )

        # Process task
        result = process_task_message(task_data, "high")

        status_code = 200 if result.get("success") else 500

        return func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code,
            headers=add_cors_headers({})
        )

    except Exception as e:
        logger.error(f"Error in high priority task endpoint: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500,
            headers=add_cors_headers({})
        )


@bp.route(route="task/medium", methods=["POST"])
def process_medium_priority_task(req: func.HttpRequest) -> func.HttpResponse:
    """Process medium priority task"""
    try:
        # Parse request body
        try:
            task_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )

        if not task_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )

        # Process task
        result = process_task_message(task_data, "medium")

        status_code = 200 if result.get("success") else 500

        return func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code,
            headers=add_cors_headers({})
        )

    except Exception as e:
        logger.error(f"Error in medium priority task endpoint: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500,
            headers=add_cors_headers({})
        )


@bp.route(route="task/low", methods=["POST"])
def process_low_priority_task(req: func.HttpRequest) -> func.HttpResponse:
    """Process low priority task"""
    try:
        # Parse request body
        try:
            task_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )

        if not task_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400,
                headers=add_cors_headers({})
            )

        # Process task
        result = process_task_message(task_data, "low")

        status_code = 200 if result.get("success") else 500

        return func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code,
            headers=add_cors_headers({})
        )

    except Exception as e:
        logger.error(f"Error in low priority task endpoint: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500,
            headers=add_cors_headers({})
        )
