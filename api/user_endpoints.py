"""
User API Endpoints

This module provides HTTP endpoints for user-related database operations.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any

from repositories.user_repository import UserRepository

logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Initialize repository
user_repo = UserRepository()


@bp.route(route="users", methods=["GET"])
def list_users(req: func.HttpRequest) -> func.HttpResponse:
    """
    List all users with optional filtering

    Query Parameters:
    - account_id: Filter by account ID
    - is_active: Filter by active status (true/false)

    Returns:
        JSON array of user objects
    """
    try:
        # Get query parameters
        account_id = req.params.get('account_id')
        is_active = req.params.get('is_active')

        # Convert parameters to appropriate types
        if account_id:
            account_id = int(account_id)
        if is_active:
            is_active = is_active.lower() == 'true'

        # Get users
        users = user_repo.list_users(account_id=account_id, is_active=is_active)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": users,
                "count": len(users)
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error listing users: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/{user_id}", methods=["GET"])
def get_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get a specific user by ID

    Path Parameters:
    - user_id: User ID

    Returns:
        JSON user object
    """
    try:
        user_id = req.route_params.get('user_id')
        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        user = user_repo.get_user_by_id(int(user_id))

        if not user:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User not found"
                }),
                mimetype="application/json",
                status_code=404
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": user
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting user: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/email/{email}", methods=["GET"])
def get_user_by_email(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get a user by email address

    Path Parameters:
    - email: User email

    Returns:
        JSON user object
    """
    try:
        email = req.route_params.get('email')
        if not email:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Email is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        user = user_repo.get_user_by_email(email)

        if not user:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User not found"
                }),
                mimetype="application/json",
                status_code=404
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": user
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting user by email: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users", methods=["POST"])
def create_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create a new user

    Request Body:
    {
        "name": "User Name",
        "email": "<EMAIL>",
        "phone": "**********",
        "account_id": 1
    }

    Returns:
        JSON object with user_id
    """
    try:
        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        if not req_body.get('email'):
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Email is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Create user
        user_id = user_repo.create_user(req_body)

        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create user"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {
                    "user_id": user_id
                }
            }),
            mimetype="application/json",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/{user_id}", methods=["PUT"])
def update_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Update a user

    Path Parameters:
    - user_id: User ID

    Request Body:
    {
        "name": "Updated Name",
        "phone": "9876543210",
        "is_active": true
    }

    Returns:
        JSON success response
    """
    try:
        user_id = req.route_params.get('user_id')
        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        req_body = req.get_json()

        # Update user
        success = user_repo.update_user(int(user_id), req_body)

        if not success:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to update user"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "User updated successfully"
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error updating user: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/{user_id}", methods=["DELETE"])
def delete_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Delete a user (soft delete)

    Path Parameters:
    - user_id: User ID

    Returns:
        JSON success response
    """
    try:
        user_id = req.route_params.get('user_id')
        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Delete user
        success = user_repo.delete_user(int(user_id))

        if not success:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to delete user"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "User deleted successfully"
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error deleting user: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/{user_id}/login", methods=["POST"])
def create_user_login(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create login credentials for a user

    Path Parameters:
    - user_id: User ID

    Request Body:
    {
        "username": "<EMAIL>",
        "password_hash": "hashed_password",
        "salt": "salt_value"
    }

    Returns:
        JSON success response
    """
    try:
        user_id = req.route_params.get('user_id')
        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        required_fields = ['username', 'password_hash', 'salt']
        for field in required_fields:
            if not req_body.get(field):
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"{field} is required"
                    }),
                    mimetype="application/json",
                    status_code=400
                )

        # Create login
        success = user_repo.create_user_login(
            int(user_id),
            req_body['username'],
            req_body['password_hash'],
            req_body['salt']
        )

        if not success:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create user login"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "User login created successfully"
            }),
            mimetype="application/json",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Error creating user login: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/login/verify", methods=["POST"])
def verify_login(req: func.HttpRequest) -> func.HttpResponse:
    """
    Verify user login credentials

    Request Body:
    {
        "username": "<EMAIL>",
        "password_hash": "hashed_password"
    }

    Returns:
        JSON object with user_id if valid
    """
    try:
        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        if not req_body.get('username') or not req_body.get('password_hash'):
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Username and password_hash are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Verify login
        user_id = user_repo.verify_user_login(
            req_body['username'],
            req_body['password_hash']
        )

        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid credentials"
                }),
                mimetype="application/json",
                status_code=401
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {
                    "user_id": user_id
                }
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error verifying login: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/login", methods=["POST"])
def user_login(req: func.HttpRequest) -> func.HttpResponse:
    """
    User login endpoint

    Request Body:
    {
        "email": "<EMAIL>",
        "password": "user_password"
    }

    Returns:
        JSON response with access token and user info
    """
    import secrets
    import jwt
    from datetime import datetime, timedelta, timezone

    try:
        req_body = req.get_json()
        if not req_body or not all(k in req_body for k in ["email", "password"]):
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Email and password are required"}),
                mimetype="application/json",
                status_code=400
            )

        email = req_body.get('email', '').strip().lower()
        password = req_body.get('password', '')

        if not email or not password:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Email and password cannot be empty"}),
                mimetype="application/json",
                status_code=400
            )

        # Get user account by email
        user_account = user_repo.get_user_by_email(email)
        if not user_account:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Invalid credentials"}),
                mimetype="application/json",
                status_code=401
            )

        # Verify login using the repository method (it handles password hashing internally)
        user_id = user_repo.verify_user_login(email, password)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Invalid credentials"}),
                mimetype="application/json",
                status_code=401
            )

        # Create JWT tokens
        from shared.config import get_jwt_config
        jwt_config = get_jwt_config()

        # Create access token
        access_token_payload = {
            "sub": email,
            "user_id": user_id,
            "exp": datetime.now(timezone.utc) + timedelta(minutes=jwt_config['access_token_expire_minutes'])
        }
        access_token = jwt.encode(access_token_payload, jwt_config['secret'], algorithm=jwt_config['algorithm'])

        # Create refresh token
        refresh_token = secrets.token_urlsafe(32)

        # Prepare user info
        full_name = f"{user_account.get('FirstName', '')} {user_account.get('LastName', '')}".strip()

        response_data = {
            "success": True,
            "data": {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "user": {
                    "email": email,
                    "name": full_name,
                    "id": user_id
                }
            }
        }

        return func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error during login: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": "Login failed. Please try again."
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="auth/signup", methods=["POST"])
def signup(req: func.HttpRequest) -> func.HttpResponse:
    """
    User registration endpoint
    """
    import secrets
    from datetime import datetime, timedelta
    try:
        req_body = req.get_json()
        if not req_body or not all(k in req_body for k in ["email", "password"]):
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Email and password are required"}),
                mimetype="application/json",
                status_code=400
            )
        email = req_body.get("email")
        password = req_body.get("password")
        full_name = req_body.get("name", "")
        name_parts = full_name.split(" ", 2)
        first_name = name_parts[0] if len(name_parts) > 0 else ""
        middle_name = ""
        last_name = ""
        if len(name_parts) == 2:
            last_name = name_parts[1]
        elif len(name_parts) > 2:
            middle_name = name_parts[1]
            last_name = name_parts[2]
        dob = req_body.get("dob", "")
        contact = req_body.get("contact", "")
        state = req_body.get("state", "")
        country = req_body.get("country", "")
        organization = req_body.get("organization", "")
        # Check if user already exists
        existing_user = user_repo.get_user_by_email(email)
        if existing_user:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "User already exists"}),
                mimetype="application/json",
                status_code=400
            )
        # Create user with password
        user_id = user_repo.create_user_with_password(
            email=email,
            password=password,
            first_name=first_name,
            middle_name=middle_name,
            last_name=last_name,
            dob=dob,
            contact=contact,
            state=state,
            country=country,
            organization=organization
        )
        if not user_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Failed to create user"}),
                mimetype="application/json",
                status_code=500
            )
        # Generate JWT tokens
        import jwt
        from datetime import datetime, timedelta, timezone
        from shared.config import get_jwt_config

        jwt_config = get_jwt_config()

        # Create access token
        access_token_payload = {
            "sub": email,
            "user_id": user_id,
            "exp": datetime.now(timezone.utc) + timedelta(minutes=jwt_config['access_token_expire_minutes'])
        }
        access_token = jwt.encode(access_token_payload, jwt_config['secret'], algorithm=jwt_config['algorithm'])

        # Create refresh token
        refresh_token = secrets.token_urlsafe(32)

        full_name = f"{first_name} {middle_name} {last_name}".replace("  ", " ").strip()
        response_data = {
            "success": True,
            "data": {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "user": {
                    "email": email,
                    "name": full_name,
                    "id": user_id
                }
            }
        }
        return func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        return func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )