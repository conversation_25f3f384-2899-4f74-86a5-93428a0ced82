"""
User API Endpoints

This module provides HTTP endpoints for user-related database operations.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any

from repositories.user_repository import UserRepository

logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Initialize repository
user_repo = UserRepository()


@bp.route(route="users", methods=["GET"])
def list_users(req: func.HttpRequest) -> func.HttpResponse:
    """
    List all users with optional filtering

    Query Parameters:
    - account_id: Filter by account ID
    - is_active: Filter by active status (true/false)

    Returns:
        JSON array of user objects
    """
    try:
        # Get query parameters
        account_id = req.params.get('account_id')
        is_active = req.params.get('is_active')

        # Convert parameters to appropriate types
        if account_id:
            account_id = int(account_id)
        if is_active:
            is_active = is_active.lower() == 'true'

        # Get users
        users = user_repo.list_users(account_id=account_id, is_active=is_active)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": users,
                "count": len(users)
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error listing users: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/{user_id}", methods=["GET"])
def get_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get a specific user by ID

    Path Parameters:
    - user_id: User ID

    Returns:
        JSON user object
    """
    try:
        user_id = req.route_params.get('user_id')
        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        user = user_repo.get_user_by_id(int(user_id))

        if not user:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User not found"
                }),
                mimetype="application/json",
                status_code=404
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": user
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting user: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/email/{email}", methods=["GET"])
def get_user_by_email(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get a user by email address

    Path Parameters:
    - email: User email

    Returns:
        JSON user object
    """
    try:
        email = req.route_params.get('email')
        if not email:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Email is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        user = user_repo.get_user_by_email(email)

        if not user:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User not found"
                }),
                mimetype="application/json",
                status_code=404
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": user
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting user by email: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users", methods=["POST"])
def create_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create a new user

    Request Body:
    {
        "name": "User Name",
        "email": "<EMAIL>",
        "phone": "**********",
        "account_id": 1
    }

    Returns:
        JSON object with user_id
    """
    try:
        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        if not req_body.get('email'):
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Email is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Create user
        user_id = user_repo.create_user(req_body)

        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create user"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {
                    "user_id": user_id
                }
            }),
            mimetype="application/json",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/{user_id}", methods=["PUT"])
def update_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Update a user

    Path Parameters:
    - user_id: User ID

    Request Body:
    {
        "name": "Updated Name",
        "phone": "9876543210",
        "is_active": true
    }

    Returns:
        JSON success response
    """
    try:
        user_id = req.route_params.get('user_id')
        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        req_body = req.get_json()

        # Update user
        success = user_repo.update_user(int(user_id), req_body)

        if not success:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to update user"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "User updated successfully"
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error updating user: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="users/{user_id}", methods=["DELETE"])
def delete_user(req: func.HttpRequest) -> func.HttpResponse:
    """
    Delete a user (soft delete)

    Path Parameters:
    - user_id: User ID

    Returns:
        JSON success response
    """
    try:
        user_id = req.route_params.get('user_id')
        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "User ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Delete user
        success = user_repo.delete_user(int(user_id))

        if not success:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to delete user"
                }),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "User deleted successfully"
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error deleting user: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


# Password-based user login creation removed - Azure AD authentication only


# Password-based login verification removed - Azure AD authentication only


# Password-based user login removed - Azure AD authentication only


# Email/password signup endpoint removed - Azure AD authentication only