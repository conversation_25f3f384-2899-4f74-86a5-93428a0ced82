"""
User Profile Endpoints

This module provides endpoints for user profile management operations.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any, Optional
from azure.functions import Blueprint

from shared.data_access import get_table_storage_repository, get_sql_database_repository
from shared.azure_services import is_local_dev
from shared.service_bus_client import get_service_bus_client
from shared.auth_utils import get_current_user, get_user_id_from_request
from shared.cors_middleware import add_cors_headers

logger = logging.getLogger(__name__)
bp = Blueprint()

@bp.route(route="user/profile", methods=["GET", "PUT", "OPTIONS"])
def user_profile(req: func.HttpRequest) -> func.HttpResponse:
    """
    Handle user profile operations (GET and PUT)

    Returns:
        JSON response with user profile data
    """
    try:
        # Handle CORS preflight
        if req.method == "OPTIONS":
            from shared.cors_middleware import handle_cors_preflight
            return handle_cors_preflight(req)

        # Get current user information from JWT token
        current_user = get_current_user(req)
        if not current_user:
            response = func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
            return add_cors_headers(req, response)

        # Route to appropriate handler based on HTTP method
        if req.method == "GET":
            return handle_get_user_profile(req, current_user)
        elif req.method == "PUT":
            return handle_update_user_profile(req, current_user)
        else:
            response = func.HttpResponse(
                json.dumps({"error": "Method not allowed"}),
                mimetype="application/json",
                status_code=405
            )
            return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in user profile endpoint: {str(e)}")
        response = func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to process user profile request: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )
        return add_cors_headers(req, response)

def handle_get_user_profile(req: func.HttpRequest, current_user: Dict[str, Any]) -> func.HttpResponse:
    """
    Handle GET request for user profile

    Returns:
        JSON response with user profile data
    """
    try:

        # Get user ID from token or use email as fallback
        user_id = current_user.get("user_id") or current_user.get("email")
        if not user_id:
            response = func.HttpResponse(
                json.dumps({"error": "User ID not found in token"}),
                mimetype="application/json",
                status_code=401
            )
            return add_cors_headers(req, response)

        # Get user profile using the user repository
        try:
            from repositories.user_repository import UserRepository
            user_repo = UserRepository()

            # Try to get user by ID first, then by email
            user_profile = None
            if current_user.get("user_id"):
                user_profile = user_repo.get_user_by_id(current_user["user_id"])

            if not user_profile and current_user.get("email"):
                user_profile = user_repo.get_user_by_email(current_user["email"])

            if not user_profile:
                # Create a basic profile from token data for new users
                user_profile = {
                    "email": current_user.get("email", ""),
                    "name": current_user.get("name", ""),
                    "roles": current_user.get("roles", []),
                    "isAdmin": current_user.get("isAdmin", False),
                    "user_id": current_user.get("user_id", ""),
                    "first_name": current_user.get("name", "").split(" ")[0] if current_user.get("name") else "",
                    "last_name": " ".join(current_user.get("name", "").split(" ")[1:]) if current_user.get("name") and len(current_user.get("name", "").split(" ")) > 1 else "",
                    "phone": "",
                    "job_title": "",
                    "company": ""
                }

            # Remove sensitive information
            if isinstance(user_profile, dict):
                user_profile.pop("password", None)
                user_profile.pop("password_hash", None)
                user_profile.pop("PasswordHash", None)
                user_profile.pop("PasswordSalt", None)

            response = func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": user_profile
                }),
                mimetype="application/json",
                status_code=200
            )
            return add_cors_headers(req, response)

        except Exception as db_error:
            logger.error(f"Database error getting user profile: {str(db_error)}")
            # Return basic profile from token as fallback
            fallback_profile = {
                "email": current_user.get("email", ""),
                "name": current_user.get("name", ""),
                "roles": current_user.get("roles", []),
                "isAdmin": current_user.get("isAdmin", False),
                "user_id": current_user.get("user_id", ""),
                "first_name": current_user.get("name", "").split(" ")[0] if current_user.get("name") else "",
                "last_name": " ".join(current_user.get("name", "").split(" ")[1:]) if current_user.get("name") and len(current_user.get("name", "").split(" ")) > 1 else "",
                "phone": "",
                "job_title": "",
                "company": ""
            }

            response = func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": fallback_profile,
                    "message": "Using basic profile from authentication token"
                }),
                mimetype="application/json",
                status_code=200
            )
            return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        response = func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to get user profile: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )
        return add_cors_headers(req, response)

def handle_update_user_profile(req: func.HttpRequest, current_user: Dict[str, Any]) -> func.HttpResponse:
    """
    Handle PUT request for updating user profile

    Returns:
        JSON response with updated user profile
    """
    try:
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            response = func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
            return add_cors_headers(req, response)

        # For now, return success with the current user data
        # TODO: Implement actual profile update logic
        response_data = {
            "success": True,
            "data": {
                "email": current_user.get("email", ""),
                "name": current_user.get("name", ""),
                "first_name": request_data.get("first_name", current_user.get("name", "").split(" ")[0] if current_user.get("name") else ""),
                "last_name": request_data.get("last_name", " ".join(current_user.get("name", "").split(" ")[1:]) if current_user.get("name") and len(current_user.get("name", "").split(" ")) > 1 else ""),
                "phone": request_data.get("phone", ""),
                "job_title": request_data.get("job_title", ""),
                "company": request_data.get("company", ""),
                "user_id": current_user.get("user_id", "")
            },
            "message": "User profile updated successfully"
        }

        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        response = func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to update user profile: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )
        return add_cors_headers(req, response)



@bp.route(route="user/password", methods=["PUT", "OPTIONS"])
def update_user_password(req: func.HttpRequest) -> func.HttpResponse:
    """
    Update current user's password

    Returns:
        JSON response with success status
    """
    try:
        # Handle CORS preflight
        if req.method == "OPTIONS":
            from shared.cors_middleware import handle_cors_preflight
            return handle_cors_preflight(req)

        # Get current user information from JWT token
        current_user = get_current_user(req)
        if not current_user:
            response = func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
            return add_cors_headers(req, response)

        # For now, return a simple success response
        # TODO: Implement actual password update logic
        response = func.HttpResponse(
            json.dumps({
                "success": True,
                "message": "Password update functionality not yet implemented in separated architecture"
            }),
            mimetype="application/json",
            status_code=200
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error updating user password: {str(e)}")
        response = func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to update password: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )
        return add_cors_headers(req, response)