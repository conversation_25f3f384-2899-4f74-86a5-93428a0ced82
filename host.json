{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[3.*, 4.0.0)"}, "functionTimeout": "00:10:00", "extensions": {"http": {"routePrefix": "api/db", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true, "cors": {"allowedOrigins": ["http://localhost:3000", "https://app-atomsec-dev01.azurewebsites.net", "https://apim-atomsec-dev.azure-api.net"], "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization", "X-Requested-With", "Origin", "Accept"], "allowCredentials": true}}}}