{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "python", "AZURE_STORAGE_CONNECTION_STRING": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;", "AZURE_TENANT_ID": "your-tenant-id", "AZURE_CLIENT_ID": "your-client-id", "AZURE_CLIENT_SECRET": "your-client-secret", "KEY_VAULT_NAME": "akv-atomsec-dev", "ENVIRONMENT": "local", "IS_LOCAL_DEV": "true", "JWT_SECRET": "dev_secret_key_do_not_use_in_production", "SFDC_SERVICE_URL": "http://localhost:7071"}, "Host": {"LocalHttpPort": 7072, "CORS": "http://localhost:3000,https://app-atomsec-dev01.azurewebsites.net,**************************************", "CORSCredentials": true}}