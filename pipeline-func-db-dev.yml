# Azure DevOps Pipeline for AtomSec DB Function App (dev)
# Reference: pipeline-func-sfdc-dev.yml structure

trigger:
- dev
- dev-db

pool:
  vmImage: ubuntu-latest

steps:

- task: UsePythonVersion@0
  inputs:
    versionSpec: '3.12'
  displayName: 'Set up Python 3.12'

- task: AzureCLI@2
  inputs:
    azureSubscription: 'sc-atomsec-dev-data'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo 'Azure CLI version:'
      az --version
      echo 'Installing Azure Functions Core Tools...'
      npm install -g azure-functions-core-tools@4 --unsafe-perm true
  displayName: 'Setup Azure CLI and Functions Tools'

- script: |
    python --version
    python -m pip install --upgrade pip setuptools wheel

    echo 'Installing system dependencies for Python packages...'
    sudo apt-get update
    sudo apt-get install -y build-essential python3-dev pkg-config

    echo 'Installing ODBC driver for SQL Server on the build agent...'
    sudo apt-get install -y curl gnupg2
    
    # Remove any existing Microsoft repository entries to avoid conflicts
    sudo rm -f /etc/apt/sources.list.d/mssql-release.list
    sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
    
    # Add Microsoft repository properly
    curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo gpg --dearmor -o /usr/share/keyrings/microsoft-archive-keyring.gpg
    echo "deb [arch=amd64,arm64,armhf signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/ubuntu/$(lsb_release -rs)/prod $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/microsoft-prod.list
    
    sudo apt-get update
    sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18
    sudo ACCEPT_EULA=Y apt-get install -y unixodbc-dev

    echo 'ODBC driver installation attempt on build agent complete.'

    echo 'Installing Python dependencies ...'
    head requirements.txt
    
    # Ensure we have modern build tools compatible with Python 3.12
    pip install --upgrade pip
    pip install --upgrade setuptools>=68.0.0 wheel>=0.40.0 build>=1.0.0
    
    # Install remaining dependencies first (without pyodbc)
    pip install --no-cache-dir -r requirements.txt -t .

    # Install pyodbc to the same target directory
    pip install --no-cache-dir pyodbc==5.0.1 -t .
  displayName: 'Install ODBC driver and Python dependencies'

- script: |
    echo 'Verifying package structure before archiving...'
    echo 'Files in current directory:'
    ls -la
    
    echo 'Checking if function_app.py exists:'
    cat function_app.py | head -20
    
    echo 'Checking if host.json exists:'
    cat host.json
    
    echo 'Checking API directory:'
    ls -la api/
    
    echo 'Testing function imports locally:'
    python -c "
    try:
        import function_app
        print('✓ function_app.py imports successfully')
    except Exception as e:
        print(f'✗ function_app.py import failed: {e}')
    "
  displayName: 'Verify Package Structure'
  continueOnError: true

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    replaceExistingArchive: true
  displayName: 'Archive DB Function App'

- task: AzureFunctionApp@2
  inputs:
    connectedServiceNameARM: 'sc-atomsec-dev-data'
    appType: 'functionAppLinux'
    appName: 'func-atomsec-dbconnect-dev'
    package: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    runtimeStack: 'PYTHON|3.12'
    deploymentMethod: 'zipDeploy'
    resourceGroupName: 'atomsec-dev-data'
  displayName: 'Deploy DB Function App to Production'

- task: AzureCLI@2
  inputs:
    azureSubscription: 'sc-atomsec-dev-data'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo 'Testing production deployment...'
      
      # Wait for deployment to be ready
      sleep 60
      
      # Test health endpoint with route prefix
      echo 'Testing health endpoint with route prefix...'
      curl -v "https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db/health" || echo "Health endpoint with prefix failed"
      
      # Test health endpoint without route prefix
      echo 'Testing health endpoint without route prefix...'
      curl -v "https://func-atomsec-dbconnect-dev.azurewebsites.net/api/health" || echo "Health endpoint without prefix failed"
      
      # Test info endpoint  
      echo 'Testing info endpoint...'
      curl -v "https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db/info" || echo "Info endpoint failed"
      
      # Check function app status
      echo 'Checking function app status...'
      az functionapp show --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --query "state"
      
      echo 'Production deployment tests completed.'
  displayName: 'Test Production Deployment'
  continueOnError: true

- task: AzureCLI@2
  inputs:
    azureSubscription: 'sc-atomsec-dev-data'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo 'Checking deployment logs and function status...'
      
      # Check function app configuration
      echo 'Function app configuration:'
      az functionapp config show --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data
      
      # Check function app state
      echo 'Function app state:'
      az functionapp show --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --query "state"
      
      # Get deployment status
      echo 'Deployment status:'
      az webapp deployment list --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --query "[0].{status:status, message:message, author:author, deployer:deployer}"
      
      # Check application logs
      echo 'Getting application logs...'
      az webapp log download --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --log-file deployment.zip || echo "Could not download logs"
      
      # Try to get live logs
      echo 'Getting live application logs...'
      timeout 30s az webapp log tail --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data || echo "Live logs timeout"
      
  displayName: 'Check Deployment Status'
  continueOnError: true

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    ArtifactName: 'drop'
    publishLocation: 'Container'
  displayName: 'Publish DB Service Artifacts' 