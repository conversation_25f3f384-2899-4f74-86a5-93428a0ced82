# PowerShell script to fix Azure Key Vault permissions for the AtomSec application
# This script grants the managed identity access to the Key Vault secrets

param(
    [string]$SubscriptionId = "35518353-3fc5-49c1-91cd-3ab90df8d78d",
    [string]$ManagedIdentityAppId = "123d7a1b-7b24-4924-a73c-1fbcff016b12",
    [string]$KeyVaultName = "akv-atomsec-dev",
    [string]$ResourceGroup = "atomsec-dev-data"
)

Write-Host "🔧 Fixing Azure Key Vault permissions for AtomSec application..." -ForegroundColor Cyan
Write-Host "Managed Identity App ID: $ManagedIdentityAppId" -ForegroundColor Gray
Write-Host "Key Vault: $KeyVaultName" -ForegroundColor Gray
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Gray
Write-Host ""

# Check if Azure CLI is installed
try {
    $null = Get-Command az -ErrorAction Stop
    Write-Host "✅ Azure CLI is installed." -ForegroundColor Green
} catch {
    Write-Host "❌ Azure CLI is not installed. Please install it first." -ForegroundColor Red
    Write-Host "   Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli" -ForegroundColor Yellow
    exit 1
}

# Check if logged in
try {
    $null = az account show 2>$null
    Write-Host "✅ You are logged in to Azure CLI." -ForegroundColor Green
} catch {
    Write-Host "❌ Not logged in to Azure CLI. Please run 'az login' first." -ForegroundColor Red
    exit 1
}

# Set the subscription
Write-Host "🔄 Setting subscription to $SubscriptionId..." -ForegroundColor Yellow
az account set --subscription $SubscriptionId

# Get the object ID of the managed identity
Write-Host "🔄 Getting object ID for managed identity..." -ForegroundColor Yellow
$ObjectId = az ad sp show --id $ManagedIdentityAppId --query objectId -o tsv

if (-not $ObjectId) {
    Write-Host "❌ Could not find managed identity with App ID: $ManagedIdentityAppId" -ForegroundColor Red
    Write-Host "   Please verify the App ID is correct." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Found managed identity object ID: $ObjectId" -ForegroundColor Green

# Method 1: Using RBAC (Recommended)
Write-Host ""
Write-Host "🔄 Method 1: Granting Key Vault Secrets User role via RBAC..." -ForegroundColor Yellow

$KeyVaultScope = "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.KeyVault/vaults/$KeyVaultName"

try {
    az role assignment create --role "Key Vault Secrets User" --assignee-object-id $ObjectId --scope $KeyVaultScope 2>$null
    Write-Host "✅ Successfully granted Key Vault Secrets User role via RBAC" -ForegroundColor Green
    $RbacSuccess = $true
} catch {
    Write-Host "⚠️  RBAC assignment failed or already exists" -ForegroundColor Yellow
    $RbacSuccess = $false
}

# Method 2: Using Access Policies (Fallback)
Write-Host ""
Write-Host "🔄 Method 2: Setting Key Vault access policy (fallback)..." -ForegroundColor Yellow

try {
    az keyvault set-policy --name $KeyVaultName --object-id $ObjectId --secret-permissions get list 2>$null
    Write-Host "✅ Successfully set Key Vault access policy" -ForegroundColor Green
    $PolicySuccess = $true
} catch {
    Write-Host "❌ Failed to set Key Vault access policy" -ForegroundColor Red
    $PolicySuccess = $false
}

# Check if at least one method succeeded
if ($RbacSuccess -or $PolicySuccess) {
    Write-Host ""
    Write-Host "🎉 Key Vault permissions have been configured successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Summary:" -ForegroundColor Cyan
    Write-Host "   - Managed Identity: $ManagedIdentityAppId" -ForegroundColor Gray
    Write-Host "   - Object ID: $ObjectId" -ForegroundColor Gray
    Write-Host "   - Key Vault: $KeyVaultName" -ForegroundColor Gray
    Write-Host "   - RBAC Role Assignment: $(if ($RbacSuccess) { "✅ Success" } else { "❌ Failed" })" -ForegroundColor Gray
    Write-Host "   - Access Policy: $(if ($PolicySuccess) { "✅ Success" } else { "❌ Failed" })" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🔄 The changes may take a few minutes to propagate." -ForegroundColor Yellow
    Write-Host "   You can test the integration endpoint after waiting 2-3 minutes." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🧪 Test the fix by calling:" -ForegroundColor Cyan
    Write-Host "   curl -X GET 'https://your-function-app.azurewebsites.net/api/db/integrations' \\" -ForegroundColor Gray
    Write-Host "        -H 'Authorization: Bearer <YOUR_AZURE_AD_TOKEN>'" -ForegroundColor Gray
} else {
    Write-Host ""
    Write-Host "❌ Failed to configure Key Vault permissions using both methods." -ForegroundColor Red
    Write-Host "   Please check your permissions and try again, or contact your Azure administrator." -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "✅ Script completed successfully!" -ForegroundColor Green
