#!/bin/bash

# Script to fix Azure Key Vault permissions for the AtomSec application
# This script grants the managed identity access to the Key Vault secrets

set -e

# Configuration
MANAGED_IDENTITY_APP_ID="123d7a1b-7b24-4924-a73c-1fbcff016b12"
KEY_VAULT_NAME="akv-atomsec-dev"
RESOURCE_GROUP="atomsec-dev-data"
SUBSCRIPTION_ID="35518353-3fc5-49c1-91cd-3ab90df8d78d"

echo "🔧 Fixing Azure Key Vault permissions for AtomSec application..."
echo "Managed Identity App ID: $MANAGED_IDENTITY_APP_ID"
echo "Key Vault: $KEY_VAULT_NAME"
echo "Resource Group: $RESOURCE_GROUP"
echo ""

# Check if Azure CLI is installed and logged in
if ! command -v az &> /dev/null; then
    echo "❌ Azure CLI is not installed. Please install it first."
    echo "   Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check if logged in
if ! az account show &> /dev/null; then
    echo "❌ Not logged in to Azure CLI. Please run 'az login' first."
    exit 1
fi

echo "✅ Azure CLI is installed and you are logged in."

# Set the subscription
echo "🔄 Setting subscription to $SUBSCRIPTION_ID..."
az account set --subscription "$SUBSCRIPTION_ID"

# Get the object ID of the managed identity
echo "🔄 Getting object ID for managed identity..."
OBJECT_ID=$(az ad sp show --id "$MANAGED_IDENTITY_APP_ID" --query objectId -o tsv)

if [ -z "$OBJECT_ID" ]; then
    echo "❌ Could not find managed identity with App ID: $MANAGED_IDENTITY_APP_ID"
    echo "   Please verify the App ID is correct."
    exit 1
fi

echo "✅ Found managed identity object ID: $OBJECT_ID"

# Method 1: Using RBAC (Recommended)
echo ""
echo "🔄 Method 1: Granting Key Vault Secrets User role via RBAC..."

KEY_VAULT_SCOPE="/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.KeyVault/vaults/$KEY_VAULT_NAME"

if az role assignment create \
    --role "Key Vault Secrets User" \
    --assignee-object-id "$OBJECT_ID" \
    --scope "$KEY_VAULT_SCOPE" 2>/dev/null; then
    echo "✅ Successfully granted Key Vault Secrets User role via RBAC"
    RBAC_SUCCESS=true
else
    echo "⚠️  RBAC assignment failed or already exists"
    RBAC_SUCCESS=false
fi

# Method 2: Using Access Policies (Fallback)
echo ""
echo "🔄 Method 2: Setting Key Vault access policy (fallback)..."

if az keyvault set-policy \
    --name "$KEY_VAULT_NAME" \
    --object-id "$OBJECT_ID" \
    --secret-permissions get list 2>/dev/null; then
    echo "✅ Successfully set Key Vault access policy"
    POLICY_SUCCESS=true
else
    echo "❌ Failed to set Key Vault access policy"
    POLICY_SUCCESS=false
fi

# Check if at least one method succeeded
if [ "$RBAC_SUCCESS" = true ] || [ "$POLICY_SUCCESS" = true ]; then
    echo ""
    echo "🎉 Key Vault permissions have been configured successfully!"
    echo ""
    echo "📋 Summary:"
    echo "   - Managed Identity: $MANAGED_IDENTITY_APP_ID"
    echo "   - Object ID: $OBJECT_ID"
    echo "   - Key Vault: $KEY_VAULT_NAME"
    echo "   - RBAC Role Assignment: $([ "$RBAC_SUCCESS" = true ] && echo "✅ Success" || echo "❌ Failed")"
    echo "   - Access Policy: $([ "$POLICY_SUCCESS" = true ] && echo "✅ Success" || echo "❌ Failed")"
    echo ""
    echo "🔄 The changes may take a few minutes to propagate."
    echo "   You can test the integration endpoint after waiting 2-3 minutes."
    echo ""
    echo "🧪 Test the fix by calling:"
    echo "   curl -X GET 'https://your-function-app.azurewebsites.net/api/db/integrations' \\"
    echo "        -H 'Authorization: Bearer <YOUR_AZURE_AD_TOKEN>'"
else
    echo ""
    echo "❌ Failed to configure Key Vault permissions using both methods."
    echo "   Please check your permissions and try again, or contact your Azure administrator."
    exit 1
fi

echo ""
echo "✅ Script completed successfully!"
