"""
Queue Setup Script

This script sets up all required Azure Storage Queues for the AtomSec database service.
It creates priority-based queues for task processing.

Usage:
    python scripts/setup_queues.py
"""

import logging
import sys
import os

# Add the parent directory to the path so we can import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.queue_manager import get_queue_manager, PRIORITY_QUEUES, DEFAULT_QUEUE_NAME
from shared.common import is_local_dev

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_queues():
    """Set up all required queues"""
    logger.info("🚀 Starting queue setup...")
    
    try:
        # Get queue manager (this will initialize all queues)
        queue_manager = get_queue_manager()
        
        # List of all queues to verify
        all_queues = [DEFAULT_QUEUE_NAME] + list(PRIORITY_QUEUES.values())
        
        # Verify each queue
        successful_queues = 0
        total_queues = len(all_queues)
        
        for queue_name in all_queues:
            logger.info(f"Verifying queue: {queue_name}")
            
            try:
                # Get queue properties to verify it exists
                properties = queue_manager.get_queue_properties(queue_name)
                
                if properties:
                    message_count = properties.get("approximate_message_count", 0)
                    logger.info(f"✅ Queue {queue_name} verified (messages: {message_count})")
                    successful_queues += 1
                else:
                    logger.error(f"❌ Queue {queue_name} verification failed")
                    
            except Exception as e:
                logger.error(f"❌ Error verifying queue {queue_name}: {str(e)}")
        
        # Summary
        logger.info(f"\n📊 Queue Setup Summary:")
        logger.info(f"Environment: {'Local Development' if is_local_dev() else 'Production'}")
        logger.info(f"Successful: {successful_queues}/{total_queues}")
        logger.info(f"Failed: {total_queues - successful_queues}/{total_queues}")
        
        if successful_queues == total_queues:
            logger.info("🎉 All queues set up successfully!")
            
            # Test queue functionality
            test_queue_functionality(queue_manager)
            
            return 0
        else:
            logger.error("⚠️ Some queues failed to set up. Please check the logs.")
            return 1
            
    except Exception as e:
        logger.error(f"Error during queue setup: {str(e)}")
        return 1


def test_queue_functionality(queue_manager):
    """Test basic queue functionality"""
    logger.info("\n🧪 Testing queue functionality...")
    
    try:
        # Test message sending and receiving
        test_message = {
            "test": True,
            "message": "Queue functionality test",
            "timestamp": "2025-07-03T10:00:00Z"
        }
        
        # Test high priority queue
        test_queue = PRIORITY_QUEUES["high"]
        
        # Send test message
        logger.info(f"Sending test message to {test_queue}...")
        success = queue_manager.send_message(test_queue, test_message)
        
        if success:
            logger.info("✅ Test message sent successfully")
            
            # Receive test message
            logger.info(f"Receiving test message from {test_queue}...")
            messages = queue_manager.receive_messages(test_queue, max_messages=1)
            
            if messages and len(messages) > 0:
                received_message = messages[0]
                logger.info("✅ Test message received successfully")
                
                # Delete test message
                metadata = received_message.get("_queue_metadata", {})
                message_id = metadata.get("message_id")
                pop_receipt = metadata.get("pop_receipt")
                
                if message_id and pop_receipt:
                    delete_success = queue_manager.delete_message(test_queue, message_id, pop_receipt)
                    if delete_success:
                        logger.info("✅ Test message deleted successfully")
                    else:
                        logger.warning("⚠️ Failed to delete test message")
                else:
                    logger.warning("⚠️ Could not get message metadata for deletion")
            else:
                logger.warning("⚠️ No test message received")
        else:
            logger.error("❌ Failed to send test message")
            
        logger.info("🎯 Queue functionality test completed")
        
    except Exception as e:
        logger.error(f"Error during queue functionality test: {str(e)}")


def main():
    """Main function"""
    logger.info("Queue Setup for AtomSec Database Service")
    logger.info("=" * 50)
    
    exit_code = setup_queues()
    
    if exit_code == 0:
        logger.info("\n✅ Queue setup completed successfully!")
        logger.info("You can now use the background processor to enqueue tasks.")
    else:
        logger.error("\n❌ Queue setup failed!")
        logger.error("Please check the configuration and try again.")
    
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
