#!/bin/bash

# AtomSec Database Function App - Key Vault Secrets Setup
# This script helps you set up the required secrets in Azure Key Vault

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Set Key Vault name
KEY_VAULT_NAME="${KEY_VAULT_NAME:-akv-atomsec-dev}"

echo "=========================================="
echo "AtomSec Key Vault Secrets Setup"
echo "=========================================="
echo
echo "Key Vault: $KEY_VAULT_NAME"
echo

# Check if Key Vault exists
print_status "Checking if Key Vault exists..."
if ! az keyvault show --name "$KEY_VAULT_NAME" &> /dev/null; then
    print_error "Key Vault '$KEY_VAULT_NAME' not found. Please create it first or set KEY_VAULT_NAME environment variable."
    exit 1
fi
print_success "Key Vault found."

# Function to set secret with prompt
set_secret_with_prompt() {
    local secret_name="$1"
    local description="$2"
    local is_required="$3"
    
    echo
    print_status "Setting up secret: $secret_name"
    echo "Description: $description"
    
    # Check if secret already exists
    if az keyvault secret show --vault-name "$KEY_VAULT_NAME" --name "$secret_name" &> /dev/null; then
        print_warning "Secret '$secret_name' already exists."
        read -p "Do you want to update it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Skipping $secret_name"
            return
        fi
    fi
    
    if [[ "$is_required" == "required" ]]; then
        echo -n "Enter value for $secret_name (required): "
    else
        echo -n "Enter value for $secret_name (optional, press Enter to skip): "
    fi
    
    read -s secret_value
    echo
    
    if [[ -z "$secret_value" ]]; then
        if [[ "$is_required" == "required" ]]; then
            print_error "This secret is required. Please provide a value."
            set_secret_with_prompt "$secret_name" "$description" "$is_required"
            return
        else
            print_status "Skipping optional secret: $secret_name"
            return
        fi
    fi
    
    # Set the secret
    if az keyvault secret set --vault-name "$KEY_VAULT_NAME" --name "$secret_name" --value "$secret_value" --output none; then
        print_success "Secret '$secret_name' set successfully."
    else
        print_error "Failed to set secret '$secret_name'."
    fi
}

# Set up required secrets
print_status "Setting up required secrets..."

set_secret_with_prompt "jwt-secret" "JWT secret for token signing (generate a strong random string)" "required"

set_secret_with_prompt "sql-connection-string" "SQL Database connection string" "optional"

print_status "Setting up Azure AD secrets..."
set_secret_with_prompt "azure-ad-client-id" "Azure AD Application Client ID" "optional"
set_secret_with_prompt "azure-ad-client-secret" "Azure AD Application Client Secret" "optional"
set_secret_with_prompt "azure-ad-tenant-id" "Azure AD Tenant ID" "optional"
set_secret_with_prompt "azure-ad-redirect-uri" "Azure AD Redirect URI (e.g., https://your-frontend-url.com)" "optional"

print_status "Setting up additional secrets..."
set_secret_with_prompt "frontend-url" "Frontend application URL (e.g., https://app-atomsec-dev01.azurewebsites.net)" "optional"

echo
print_success "Key Vault secrets setup completed!"
echo
print_status "Summary of secrets in Key Vault '$KEY_VAULT_NAME':"

# List all secrets
az keyvault secret list --vault-name "$KEY_VAULT_NAME" --query "[].name" -o table

echo
print_warning "Important notes:"
echo "1. Make sure your Function App has managed identity enabled"
echo "2. Grant the Function App access to Key Vault secrets"
echo "3. Test the deployment after setting up secrets"
echo
echo "To grant Function App access to Key Vault:"
echo "az keyvault set-policy --name $KEY_VAULT_NAME --object-id <function-app-principal-id> --secret-permissions get list"
