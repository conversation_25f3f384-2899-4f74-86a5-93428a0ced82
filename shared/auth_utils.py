"""
Authentication Utilities for Database Service

This module provides authentication utilities for the database service,
including JWT token validation and user extraction from requests.
"""

import logging
import jwt
from typing import Dict, Any, Optional
import azure.functions as func
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

def get_jwt_secret() -> str:
    """Get JWT secret from environment variables"""
    # Use the same configuration as the main config module for consistency
    from shared.config import get_jwt_config
    jwt_config = get_jwt_config()
    return jwt_config['secret']

def get_token_from_header(req: func.HttpRequest) -> Optional[str]:
    """
    Extract JWT token from Authorization header

    Args:
        req: HTTP request

    Returns:
        str: JWT token or None if not found
    """
    auth_header = req.headers.get("Authorization")

    if not auth_header:
        logger.debug("No Authorization header found")
        return None

    if not auth_header.startswith("Bearer "):
        logger.debug("Authorization header does not start with 'Bearer '")
        return None

    token = auth_header[7:]  # Remove "Bearer " prefix
    logger.debug(f"Extracted token from header (length: {len(token)})")
    return token

def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decode and validate JWT token

    Args:
        token: JWT token string

    Returns:
        Dict: Token payload or None if invalid
    """
    try:
        jwt_secret = get_jwt_secret()
        payload = jwt.decode(token, jwt_secret, algorithms=['HS256'])

        # Check if token is expired
        exp = payload.get('exp')
        if exp and datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(timezone.utc):
            logger.warning("Token has expired")
            return None

        logger.debug(f"Successfully decoded token for user: {payload.get('sub')}")
        return payload

    except jwt.ExpiredSignatureError:
        logger.warning("Token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error decoding token: {str(e)}")
        return None

def get_current_user(req: func.HttpRequest) -> Optional[Dict[str, Any]]:
    """
    Get current user information from request

    Args:
        req: HTTP request

    Returns:
        Dict: User information or None if not authenticated
    """
    # Check if we're in local development mode
    from shared.azure_services import is_local_dev

    if is_local_dev():
        # In local development, try to get real user data from database
        logger.debug("Local development mode: attempting to get real user data")

        # Default email for development
        dev_email = "<EMAIL>"

        try:
            # Try to get actual user from database
            from repositories.user_repository import UserRepository
            user_repo = UserRepository()
            user_data = user_repo.get_user_by_email(dev_email)

            if user_data and user_data.get('user_id'):
                logger.debug(f"Found real user data for {dev_email}: ID {user_data.get('user_id')}")
                return {
                    "email": dev_email,
                    "user_id": str(user_data.get('user_id')),  # Use actual database ID
                    "name": user_data.get('name', 'Pranav Kumar (Dev)'),
                    "roles": ["user", "admin"],
                    "isAdmin": True
                }
            else:
                # If no user found, create one
                logger.debug(f"No user found for {dev_email}, creating new user")
                user_id = user_repo.create_user_with_password(
                    email=dev_email,
                    password="dev123",  # Development password
                    first_name="Pranav",
                    last_name="Kumar",
                    organization="AtomSec Dev"
                )

                if user_id:
                    logger.debug(f"Created new user {dev_email} with ID {user_id}")
                    return {
                        "email": dev_email,
                        "user_id": str(user_id),
                        "name": "Pranav Kumar (Dev)",
                        "roles": ["user", "admin"],
                        "isAdmin": True
                    }
        except Exception as e:
            logger.warning(f"Error getting real user data in development: {e}")

        # Fallback to mock user if database lookup fails
        logger.debug("Falling back to mock user data")
        return {
            "email": dev_email,
            "user_id": "dev-fallback",  # Fallback ID
            "name": "Pranav Kumar (Dev)",
            "roles": ["user", "admin"],
            "isAdmin": True
        }

    # Get token from header
    token = get_token_from_header(req)
    if not token:
        logger.debug("No token found in request")
        return None

    # Decode token
    payload = decode_token(token)
    if not payload:
        logger.debug("Invalid or expired token")
        return None

    # Extract user information
    email = payload.get("sub")
    user_id = payload.get("user_id")

    if not email:
        logger.warning("No email found in token")
        return None

    user_info = {
        "email": email,
        "user_id": user_id,
        "name": payload.get("name", ""),
        "roles": payload.get("roles", []),
        "isAdmin": payload.get("isAdmin", False)
    }

    logger.debug(f"Retrieved user info for: {email}")
    return user_info

def get_user_id_from_request(req: func.HttpRequest) -> Optional[str]:
    """
    Extract user ID from authenticated request

    Args:
        req: HTTP request

    Returns:
        str: User ID or None if not authenticated
    """
    user = get_current_user(req)
    if not user:
        return None

    # Try to get user_id from token first
    user_id = user.get("user_id")
    if user_id:
        logger.debug(f"Found user_id in token: {user_id}")
        return str(user_id)

    # Fallback to email as user identifier
    email = user.get("email")
    if email:
        logger.debug(f"Using email as user identifier: {email}")
        return email

    logger.warning("No user identifier found in token")
    return None

def require_auth(original_func):
    """
    Decorator to require authentication for endpoint functions

    Args:
        original_func: Original function to wrap

    Returns:
        Wrapped function that requires authentication
    """
    def wrapper(req: func.HttpRequest) -> func.HttpResponse:
        # Check if we're in local development mode
        from shared.azure_services import is_local_dev

        if is_local_dev():
            # In local development, skip authentication
            logger.debug("Local development mode: skipping authentication")
            return original_func(req)

        # Get current user for production
        current_user = get_current_user(req)
        if not current_user:
            import json
            logger.warning("Authentication required but no valid user found")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Unauthorized - valid authentication required"
                }),
                mimetype="application/json",
                status_code=401
            )

        # Add user to request for use in the endpoint
        setattr(req, "user", current_user)

        # Call original function
        return original_func(req)

    # Copy function attributes to wrapper
    import functools
    functools.update_wrapper(wrapper, original_func)

    return wrapper

def get_user_from_request_or_default(req: func.HttpRequest, default_user_id: str = "system") -> str:
    """
    Get user ID from request or return default

    Args:
        req: HTTP request
        default_user_id: Default user ID to use if authentication fails

    Returns:
        str: User ID or default value
    """
    user_id = get_user_id_from_request(req)
    if user_id:
        return user_id

    logger.debug(f"No authenticated user found, using default: {default_user_id}")
    return default_user_id
