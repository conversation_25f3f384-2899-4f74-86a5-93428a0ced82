"""
Configuration Module

This module provides centralized configuration management for the application.
It handles environment variables, secrets, and configuration settings.

Best practices implemented:
- No hardcoded secrets
- Secure retrieval of secrets from Key Vault
- Environment-specific configuration
- Proper error handling and logging
"""

import os
import logging
from typing import Dict, Any, Optional

# Configure module-level logger
logger = logging.getLogger(__name__)

# Import common functions
from shared.common import is_local_dev, is_test_env

# Azure AD Configuration
def get_azure_ad_config() -> Dict[str, str]:
    """
    Get Azure AD configuration from environment or Key Vault

    Returns:
        Dict: Azure AD configuration
    """
    from shared.azure_services import get_secret

    # Default configuration
    config = {
        "client_id": None,
        "client_secret": None,
        "tenant_id": None,
        "redirect_uri": None,
        "scope": "openid profile email User.Read"
    }

    if is_local_dev():
        # For local development, use environment variables
        config.update({
            "client_id": os.environ.get("AZURE_AD_CLIENT_ID"),
            "client_secret": os.environ.get("AZURE_AD_CLIENT_SECRET"),
            "tenant_id": os.environ.get("AZURE_AD_TENANT_ID"),
            "redirect_uri": os.environ.get("AZURE_AD_REDIRECT_URI", "http://localhost:3000")
        })
    else:
        # For production, get from Key Vault
        try:
            config.update({
                "client_id": get_secret("azure-ad-client-id"),
                "client_secret": get_secret("azure-ad-client-secret"),
                "tenant_id": get_secret("azure-ad-tenant-id"),
                "redirect_uri": get_secret("azure-ad-redirect-uri")
            })
        except Exception as e:
            logger.error(f"Error getting Azure AD configuration from Key Vault: {str(e)}")
            # Fallback to environment variables
            config.update({
                "client_id": os.environ.get("AZURE_AD_CLIENT_ID"),
                "client_secret": os.environ.get("AZURE_AD_CLIENT_SECRET"),
                "tenant_id": os.environ.get("AZURE_AD_TENANT_ID"),
                "redirect_uri": os.environ.get("AZURE_AD_REDIRECT_URI", "http://localhost:3000")
            })

    # Set authority URL
    if config["tenant_id"]:
        config["authority"] = f"https://login.microsoftonline.com/{config['tenant_id']}"
    else:
        config["authority"] = os.environ.get("AZURE_AD_AUTHORITY")

    # Validate configuration
    missing_keys = [key for key, value in config.items() if value is None]
    if missing_keys:
        logger.warning(f"Missing Azure AD configuration keys: {', '.join(missing_keys)}")

    return config

# Key Vault Configuration
def get_key_vault_url() -> str:
    """
    Get Key Vault URL from environment or default

    Returns:
        str: Key Vault URL
    """
    return os.environ.get("KEY_VAULT_URL", "https://akv-atomsec-dev.vault.azure.net/")

# JWT Configuration
def get_jwt_config() -> Dict[str, Any]:
    """
    Get JWT configuration

    Returns:
        Dict: JWT configuration
    """
    from shared.azure_services import get_secret

    config = {
        "algorithm": "HS256",
        "access_token_expire_minutes": 30,
        "refresh_token_expire_days": 7
    }

    # Get JWT secret
    if is_test_env() or is_local_dev():
        # For tests and local development, use a fixed secret
        # This is still not ideal, but better than hardcoding in multiple places
        config["secret"] = os.environ.get("JWT_SECRET", "dev_secret_key_do_not_use_in_production")
        if config["secret"] == "dev_secret_key_do_not_use_in_production":
            logger.warning("Using development JWT secret - NOT SECURE FOR PRODUCTION")
    else:
        # For production, get from Key Vault
        try:
            config["secret"] = get_secret("jwt-secret")
            if not config["secret"]:
                raise ValueError("JWT secret not found in Key Vault")
        except Exception as e:
            logger.error(f"Error getting JWT secret: {str(e)}")
            # In production, we should not fall back to a default secret
            # Instead, we should fail securely
            raise

    return config

# Storage Configuration
def get_storage_connection_string() -> str:
    """
    Get Azure Storage connection string

    Returns:
        str: Storage connection string
    """
    from shared.azure_services import get_secret

    try:
        if is_local_dev():
            logger.info("Using Azurite for local development")
            # Check if environment variable is set
            env_connection_string = os.environ.get("AzureStorageConnectionString")
            if env_connection_string:
                logger.info("Using storage connection string from environment variable")
                return env_connection_string

            # Use default Azurite connection string
            default_azurite_connection_string = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;"
            logger.info("Using default Azurite connection string")

            # Verify that the connection string is valid
            if "AccountName" not in default_azurite_connection_string or "AccountKey" not in default_azurite_connection_string:
                logger.error("Default Azurite connection string is invalid")
                raise ValueError("Default Azurite connection string is invalid")

            return default_azurite_connection_string
        else:
            logger.info("Getting storage connection string for production environment")

            # Debug: Log all environment variables that contain "STORAGE" or "AZURE"
            storage_env_vars = {k: v for k, v in os.environ.items() if 'STORAGE' in k.upper() or 'AZURE' in k.upper()}
            logger.info(f"Available storage/azure environment variables: {list(storage_env_vars.keys())}")

            # Get from environment or Key Vault
            connection_string = os.environ.get("AZURE_STORAGE_CONNECTION_STRING")
            logger.info(f"AZURE_STORAGE_CONNECTION_STRING found: {bool(connection_string)}")
            if connection_string:
                logger.info("Using storage connection string from environment variable")
                return connection_string

            # Try to get from Key Vault
            logger.info("Attempting to get storage connection string from Key Vault")
            try:
                connection_string = get_secret("storage-connection-string")
                if connection_string:
                    logger.info("Successfully retrieved storage connection string from Key Vault")
                    return connection_string
                else:
                    logger.error("Storage connection string not found in Key Vault")
                    raise ValueError("Storage connection string not found in Key Vault")
            except Exception as e:
                logger.error(f"Failed to get storage connection string from Key Vault: {str(e)}")
                import traceback
                logger.error(f"Key Vault error traceback: {traceback.format_exc()}")
                raise
    except Exception as e:
        logger.error(f"Error in get_storage_connection_string: {str(e)}")
        import traceback
        logger.error(f"Error traceback: {traceback.format_exc()}")
        raise

# SQL Database Configuration
def get_sql_connection_string() -> str:
    """
    Get SQL Database connection string

    Returns:
        str: SQL connection string
    """
    from shared.azure_services import get_secret

    if is_local_dev():
        return os.environ.get("SQL_CONNECTION_STRING", "")
    else:
        try:
            return get_secret("sql-connection-string")
        except Exception as e:
            logger.error(f"Error getting SQL connection string: {str(e)}")
            return os.environ.get("SQL_CONNECTION_STRING", "")

# Frontend URL Configuration
def get_frontend_url() -> str:
    """
    Get frontend URL from environment or Key Vault

    Returns:
        str: Frontend URL
    """
    from shared.azure_services import get_secret

    # First check environment variables (highest priority)
    frontend_url = os.environ.get("FRONTEND_URL")
    if frontend_url:
        logger.info(f"Using frontend URL from environment variable: {frontend_url}")
        return frontend_url

    # For local development, use default
    if is_local_dev():
        default_url = "http://localhost:3000"
        logger.info(f"Using default frontend URL for local development: {default_url}")
        return default_url

    # For production, try to get from Key Vault
    try:
        frontend_url = get_secret("frontend-url")
        if frontend_url:
            logger.info(f"Retrieved frontend URL from Key Vault: {frontend_url}")
            return frontend_url
        else:
            logger.warning("Frontend URL not found in Key Vault, using default")
    except Exception as e:
        logger.warning(f"Error getting frontend URL from Key Vault: {str(e)}")

    # Fallback to default production URL
    default_prod_url = "https://app-atomsec-dev01.azurewebsites.net"
    logger.info(f"Using default production frontend URL: {default_prod_url}")
    return default_prod_url

def get_backend_url() -> str:
    """
    Get backend URL based on environment

    Returns:
        str: Backend URL
    """
    # For local development, use default
    if is_local_dev():
        default_url = "http://localhost:7071"
        logger.info(f"Using default backend URL for local development: {default_url}")
        return default_url

    # For production, use default
    default_prod_url = "https://apim-atomsec-dev.azure-api.net/"
    logger.info(f"Using default production backend URL: {default_prod_url}")
    return default_prod_url

def get_api_base_url() -> str:
    """
    Get API base URL for tests and integrations

    This function is used by test_integration_tabs.py and other integration tests
    to determine the base URL for API requests.

    Returns:
        str: API base URL
    """
    # Check if base_url is set in environment (used by tests)
    env_base_url = os.environ.get("base_url")
    if env_base_url:
        # Remove trailing slash if present
        if env_base_url.endswith("/"):
            env_base_url = env_base_url[:-1]
        logger.info(f"Using API base URL from environment: {env_base_url}")
        return env_base_url

    # Otherwise, use the backend URL
    backend_url = get_backend_url()
    logger.info(f"Using backend URL as API base URL: {backend_url}")
    return backend_url

# Database Service Configuration
def get_db_service_config() -> Dict[str, Any]:
    """
    Get database service configuration
    
    Returns:
        Dict: Database service configuration
    """
    config = {
        "base_url": "http://localhost:7072",  # Default to local DB service
        "timeout": 30,
        "retry_attempts": 3,
        "retry_delay": 1,
        "user_agent": "atomsec-func-db/1.0.0",
        "api_key": None
    }
    
    # Override with environment variables if available
    if os.environ.get("DB_SERVICE_BASE_URL"):
        config["base_url"] = os.environ.get("DB_SERVICE_BASE_URL")
    
    if os.environ.get("DB_SERVICE_TIMEOUT"):
        config["timeout"] = int(os.environ.get("DB_SERVICE_TIMEOUT"))
    
    if os.environ.get("DB_SERVICE_RETRY_ATTEMPTS"):
        config["retry_attempts"] = int(os.environ.get("DB_SERVICE_RETRY_ATTEMPTS"))
    
    if os.environ.get("DB_SERVICE_RETRY_DELAY"):
        config["retry_delay"] = int(os.environ.get("DB_SERVICE_RETRY_DELAY"))
    
    if os.environ.get("DB_SERVICE_API_KEY"):
        config["api_key"] = os.environ.get("DB_SERVICE_API_KEY")
    
    return config
