"""
CORS Middleware for Database Service

This module provides CORS (Cross-Origin Resource Sharing) middleware for the database service.
It handles preflight requests and adds appropriate CORS headers to responses.
"""

import logging
import azure.functions as func
from shared.config import is_local_dev

# Configure module-level logger
logger = logging.getLogger(__name__)

def add_cors_headers(req: func.HttpRequest, response: func.HttpResponse) -> func.HttpResponse:
    """
    Add CORS headers to the response

    Args:
        req: HTTP request
        response: HTTP response

    Returns:
        func.HttpResponse: HTTP response with CORS headers
    """
    # Define allowed origins
    allowed_origins = [
        "http://localhost:3000", 
        "http://localhost:7071", 
        "http://localhost:7072",
        "https://app-atomsec-dev01.azurewebsites.net", 
        "https://apim-atomsec-dev.azure-api.net",
        "https://login.windows.net", 
        "https://login.microsoftonline.com"
    ]

    # In production, get from environment or Key Vault
    if not is_local_dev():
        try:
            from shared.azure_services import get_secret
            frontend_url = get_secret("frontend-url")
            if frontend_url and frontend_url not in allowed_origins:
                allowed_origins.append(frontend_url)
        except Exception as e:
            logger.error(f"Error getting frontend URL: {str(e)}")
            # Add any production URLs as fallback
            pass

    # Get the origin from the request
    origin = req.headers.get("Origin", "")
    
    # For Azure API Management, also check for the APIM origin
    if not origin and req.headers.get("X-Forwarded-Host"):
        # Handle requests coming through Azure API Management
        forwarded_host = req.headers.get("X-Forwarded-Host")
        if "apim-atomsec-dev.azure-api.net" in forwarded_host:
            origin = "https://app-atomsec-dev01.azurewebsites.net"

    # Check if the origin is allowed or if it's a same-origin request
    if origin in allowed_origins or not origin:
        # Add CORS headers to the response
        response.headers["Access-Control-Allow-Origin"] = origin if origin else "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, Origin, Accept"
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Max-Age"] = "86400"

    # Handle preflight requests
    if req.method == "OPTIONS":
        return func.HttpResponse(
            status_code=204,
            headers={
                "Access-Control-Allow-Origin": origin if origin in allowed_origins else "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Origin, Accept",
                "Access-Control-Allow-Credentials": "true",
                "Access-Control-Max-Age": "86400"
            }
        )

    return response

def handle_cors_preflight(req: func.HttpRequest) -> func.HttpResponse:
    """
    Handle CORS preflight requests

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response for preflight request
    """
    logger.info('Processing CORS preflight request...')

    # Define allowed origins
    allowed_origins = [
        "http://localhost:3000", 
        "http://localhost:7071", 
        "http://localhost:7072",
        "https://app-atomsec-dev01.azurewebsites.net", 
        "https://apim-atomsec-dev.azure-api.net",
        "https://login.windows.net", 
        "https://login.microsoftonline.com"
    ]

    # Get the origin from the request
    origin = req.headers.get("Origin", "")
    
    # For Azure API Management, also check for the APIM origin
    if not origin and req.headers.get("X-Forwarded-Host"):
        # Handle requests coming through Azure API Management
        forwarded_host = req.headers.get("X-Forwarded-Host")
        if "apim-atomsec-dev.azure-api.net" in forwarded_host:
            origin = "https://app-atomsec-dev01.azurewebsites.net"

    # Return CORS headers
    return func.HttpResponse(
        status_code=204,
        headers={
            "Access-Control-Allow-Origin": origin if origin in allowed_origins else "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Origin, Accept",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "86400"
        }
    )
