"""
Execution Log Service Module

This module provides centralized management of execution logs for tracking
long-running operations, scans, and background processes.

Features:
- Create and manage execution logs
- Track execution status and progress
- Support for both local development and production environments
- Integration with Azure Table Storage and SQL Database
- Proper error handling and logging
- Clean code principles and best practices

Best practices implemented:
- Single responsibility principle
- Dependency injection
- Environment-aware configuration
- Comprehensive error handling
- Consistent logging patterns
- Type hints for better code clarity
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Union

# Import shared modules
from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository

# Configure module-level logger
logger = logging.getLogger(__name__)

# Execution status constants
EXECUTION_STATUS_PENDING = "Pending"
EXECUTION_STATUS_RUNNING = "Running"
EXECUTION_STATUS_COMPLETED = "Completed"
EXECUTION_STATUS_FAILED = "Failed"
EXECUTION_STATUS_CANCELLED = "Cancelled"

# Global repository instances (lazy initialized)
_execution_log_table_repo = None
_execution_log_sql_repo = None


class ExecutionLogService:
    """
    Service class for managing execution logs
    
    This service provides a clean interface for creating, updating, and querying
    execution logs while abstracting the underlying storage mechanism.
    """
    
    def __init__(self):
        """Initialize the execution log service"""
        self.table_repo = None
        self.sql_repo = None
        self._initialize_repositories()
    
    def _initialize_repositories(self) -> None:
        """Initialize the appropriate repository based on environment"""
        try:
            if is_local_dev():
                # Use Table Storage for local development
                self.table_repo = self._get_table_repository()
                logger.info("Initialized execution log service with Table Storage for local development")
            else:
                # Use SQL Database for production
                self.sql_repo = self._get_sql_repository()
                logger.info("Initialized execution log service with SQL Database for production")
        except Exception as e:
            logger.error(f"Error initializing execution log service repositories: {str(e)}")
    
    def _get_table_repository(self) -> Optional[TableStorageRepository]:
        """Get or create Table Storage repository"""
        global _execution_log_table_repo
        
        if _execution_log_table_repo is None:
            try:
                _execution_log_table_repo = TableStorageRepository("ExecutionLogs")
                logger.debug("Created Table Storage repository for execution logs")
            except Exception as e:
                logger.error(f"Failed to create Table Storage repository: {str(e)}")
                return None
        
        return _execution_log_table_repo
    
    def _get_sql_repository(self) -> Optional[SqlDatabaseRepository]:
        """Get or create SQL Database repository"""
        global _execution_log_sql_repo
        
        if _execution_log_sql_repo is None:
            try:
                _execution_log_sql_repo = SqlDatabaseRepository("App_ExecutionLog")
                logger.debug("Created SQL Database repository for execution logs")
            except Exception as e:
                logger.error(f"Failed to create SQL Database repository: {str(e)}")
                return None
        
        return _execution_log_sql_repo
    
    def create_execution_log(
        self,
        org_id: Union[str, int],
        execution_type: str,
        user_id: Union[str, int],
        priority: str = "Medium",
        status: str = EXECUTION_STATUS_PENDING
    ) -> Optional[str]:
        """
        Create a new execution log entry
        
        Args:
            org_id: Organization ID (can be string UUID or integer)
            execution_type: Type of execution (e.g., "Integration_Scan", "Data_Export")
            user_id: User ID who initiated the execution
            priority: Priority level (High, Medium, Low)
            status: Initial status (default: Pending)
        
        Returns:
            str: Execution log ID if successful, None otherwise
        """
        try:
            # Generate unique execution log ID
            execution_log_id = str(uuid.uuid4())
            
            # Convert org_id to appropriate type based on environment
            if is_local_dev():
                # For local development, keep as string for Table Storage
                org_id_value = str(org_id)
            else:
                # For production SQL, convert to integer if possible
                try:
                    org_id_value = int(org_id) if str(org_id).isdigit() else 0
                except (ValueError, TypeError):
                    org_id_value = 0
            
            # Convert user_id to integer
            try:
                user_id_value = int(user_id) if str(user_id).isdigit() else 1
            except (ValueError, TypeError):
                user_id_value = 1
            
            if is_local_dev():
                return self._create_execution_log_table(
                    execution_log_id, org_id_value, execution_type, 
                    user_id_value, priority, status
                )
            else:
                return self._create_execution_log_sql(
                    org_id_value, execution_type, user_id_value, status
                )
                
        except Exception as e:
            logger.error(f"Error creating execution log: {str(e)}")
            return None
    
    def _create_execution_log_table(
        self,
        execution_log_id: str,
        org_id: str,
        execution_type: str,
        user_id: int,
        priority: str,
        status: str
    ) -> Optional[str]:
        """Create execution log in Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return None
            
            # Create entity for Table Storage
            entity = {
                "PartitionKey": f"execution_log_{org_id}",
                "RowKey": execution_log_id,
                "ExecutionLogId": execution_log_id,
                "OrgId": org_id,
                "ExecutionType": execution_type,
                "Status": status,
                "Priority": priority,
                "StartTime": datetime.now().isoformat(),
                "EndTime": None,
                "ExecutedBy": user_id,
                "CreatedAt": datetime.now().isoformat(),
                "UpdatedAt": datetime.now().isoformat()
            }
            
            success = self.table_repo.insert_entity(entity)
            if success:
                logger.info(f"Created execution log in Table Storage: {execution_log_id}")
                return execution_log_id
            else:
                logger.error(f"Failed to create execution log in Table Storage: {execution_log_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating execution log in Table Storage: {str(e)}")
            return None
    
    def _create_execution_log_sql(
        self,
        org_id: int,
        execution_type: str,
        user_id: int,
        status: str
    ) -> Optional[str]:
        """Create execution log in SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return None
            
            # Insert execution log into SQL database
            query = """
            INSERT INTO App_ExecutionLog (OrgId, ExecutionType, Status, StartTime, ExecutedBy)
            OUTPUT INSERTED.Id
            VALUES (?, ?, ?, ?, ?)
            """
            params = (
                org_id,
                execution_type,
                status,
                datetime.now().isoformat(),
                user_id
            )
            
            results = self.sql_repo.execute_query(query, params)
            if results and len(results) > 0:
                execution_log_id = str(results[0][0])
                logger.info(f"Created execution log in SQL Database: {execution_log_id}")
                return execution_log_id
            else:
                logger.error("Failed to create execution log in SQL Database")
                return None
                
        except Exception as e:
            logger.error(f"Error creating execution log in SQL Database: {str(e)}")
            return None

    def update_execution_log_status(
        self,
        execution_log_id: str,
        status: str,
        end_time: Optional[datetime] = None
    ) -> bool:
        """
        Update the status of an execution log

        Args:
            execution_log_id: ID of the execution log to update
            status: New status to set
            end_time: Optional end time (defaults to current time if status is completed/failed)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Set end time if not provided and status indicates completion
            if end_time is None and status in [EXECUTION_STATUS_COMPLETED, EXECUTION_STATUS_FAILED, EXECUTION_STATUS_CANCELLED]:
                end_time = datetime.now()

            if is_local_dev():
                return self._update_execution_log_table(execution_log_id, status, end_time)
            else:
                return self._update_execution_log_sql(execution_log_id, status, end_time)

        except Exception as e:
            logger.error(f"Error updating execution log status: {str(e)}")
            return False

    def _update_execution_log_table(
        self,
        execution_log_id: str,
        status: str,
        end_time: Optional[datetime]
    ) -> bool:
        """Update execution log in Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return False

            # Find the entity first
            filter_query = f"RowKey eq '{execution_log_id}'"
            entities = list(self.table_repo.query_entities(filter_query))

            if not entities:
                logger.warning(f"Execution log not found: {execution_log_id}")
                return False

            entity = entities[0]
            entity["Status"] = status
            entity["UpdatedAt"] = datetime.now().isoformat()

            if end_time:
                entity["EndTime"] = end_time.isoformat()

            success = self.table_repo.update_entity(entity)
            if success:
                logger.info(f"Updated execution log status in Table Storage: {execution_log_id} -> {status}")
                return True
            else:
                logger.error(f"Failed to update execution log in Table Storage: {execution_log_id}")
                return False

        except Exception as e:
            logger.error(f"Error updating execution log in Table Storage: {str(e)}")
            return False

    def _update_execution_log_sql(
        self,
        execution_log_id: str,
        status: str,
        end_time: Optional[datetime]
    ) -> bool:
        """Update execution log in SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return False

            # Update execution log in SQL database
            if end_time:
                query = """
                UPDATE App_ExecutionLog
                SET Status = ?, EndTime = ?
                WHERE Id = ?
                """
                params = (status, end_time.isoformat(), execution_log_id)
            else:
                query = """
                UPDATE App_ExecutionLog
                SET Status = ?
                WHERE Id = ?
                """
                params = (status, execution_log_id)

            success = self.sql_repo.execute_non_query(query, params)
            if success:
                logger.info(f"Updated execution log status in SQL Database: {execution_log_id} -> {status}")
                return True
            else:
                logger.error(f"Failed to update execution log in SQL Database: {execution_log_id}")
                return False

        except Exception as e:
            logger.error(f"Error updating execution log in SQL Database: {str(e)}")
            return False

    def get_execution_log(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an execution log by ID

        Args:
            execution_log_id: ID of the execution log to retrieve

        Returns:
            Dict[str, Any]: Execution log data if found, None otherwise
        """
        try:
            if is_local_dev():
                return self._get_execution_log_table(execution_log_id)
            else:
                return self._get_execution_log_sql(execution_log_id)

        except Exception as e:
            logger.error(f"Error getting execution log: {str(e)}")
            return None

    def _get_execution_log_table(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """Get execution log from Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return None

            filter_query = f"RowKey eq '{execution_log_id}'"
            entities = list(self.table_repo.query_entities(filter_query))

            if entities:
                entity = entities[0]
                logger.debug(f"Retrieved execution log from Table Storage: {execution_log_id}")
                return dict(entity)
            else:
                logger.warning(f"Execution log not found in Table Storage: {execution_log_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting execution log from Table Storage: {str(e)}")
            return None

    def _get_execution_log_sql(self, execution_log_id: str) -> Optional[Dict[str, Any]]:
        """Get execution log from SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return None

            query = """
            SELECT Id, OrgId, ExecutionType, Status, StartTime, EndTime, ExecutedBy
            FROM App_ExecutionLog
            WHERE Id = ?
            """
            params = (execution_log_id,)

            results = self.sql_repo.execute_query(query, params)
            if results and len(results) > 0:
                row = results[0]
                execution_log = {
                    "Id": row[0],
                    "OrgId": row[1],
                    "ExecutionType": row[2],
                    "Status": row[3],
                    "StartTime": row[4],
                    "EndTime": row[5],
                    "ExecutedBy": row[6]
                }
                logger.debug(f"Retrieved execution log from SQL Database: {execution_log_id}")
                return execution_log
            else:
                logger.warning(f"Execution log not found in SQL Database: {execution_log_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting execution log from SQL Database: {str(e)}")
            return None

    def get_execution_logs_by_org(
        self,
        org_id: Union[str, int],
        limit: int = 50,
        status_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get execution logs for a specific organization

        Args:
            org_id: Organization ID
            limit: Maximum number of logs to return
            status_filter: Optional status filter

        Returns:
            List[Dict[str, Any]]: List of execution logs
        """
        try:
            if is_local_dev():
                return self._get_execution_logs_by_org_table(str(org_id), limit, status_filter)
            else:
                org_id_int = int(org_id) if str(org_id).isdigit() else 0
                return self._get_execution_logs_by_org_sql(org_id_int, limit, status_filter)

        except Exception as e:
            logger.error(f"Error getting execution logs by org: {str(e)}")
            return []

    def _get_execution_logs_by_org_table(
        self,
        org_id: str,
        limit: int,
        status_filter: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Get execution logs by organization from Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return []

            filter_query = f"PartitionKey eq 'execution_log_{org_id}'"
            if status_filter:
                filter_query += f" and Status eq '{status_filter}'"

            entities = list(self.table_repo.query_entities(filter_query))

            # Sort by creation time (most recent first) and limit
            sorted_entities = sorted(
                entities,
                key=lambda x: x.get("CreatedAt", ""),
                reverse=True
            )[:limit]

            logger.debug(f"Retrieved {len(sorted_entities)} execution logs for org {org_id}")
            return [dict(entity) for entity in sorted_entities]

        except Exception as e:
            logger.error(f"Error getting execution logs from Table Storage: {str(e)}")
            return []

    def _get_execution_logs_by_org_sql(
        self,
        org_id: int,
        limit: int,
        status_filter: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Get execution logs by organization from SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return []

            if status_filter:
                query = f"""
                SELECT TOP {limit} Id, OrgId, ExecutionType, Status, StartTime, EndTime, ExecutedBy
                FROM App_ExecutionLog
                WHERE OrgId = ? AND Status = ?
                ORDER BY StartTime DESC
                """
                params = (org_id, status_filter)
            else:
                query = f"""
                SELECT TOP {limit} Id, OrgId, ExecutionType, Status, StartTime, EndTime, ExecutedBy
                FROM App_ExecutionLog
                WHERE OrgId = ?
                ORDER BY StartTime DESC
                """
                params = (org_id,)

            results = self.sql_repo.execute_query(query, params)
            execution_logs = []

            for row in results:
                execution_log = {
                    "Id": row[0],
                    "OrgId": row[1],
                    "ExecutionType": row[2],
                    "Status": row[3],
                    "StartTime": row[4],
                    "EndTime": row[5],
                    "ExecutedBy": row[6]
                }
                execution_logs.append(execution_log)

            logger.debug(f"Retrieved {len(execution_logs)} execution logs for org {org_id}")
            return execution_logs

        except Exception as e:
            logger.error(f"Error getting execution logs from SQL Database: {str(e)}")
            return []

    def delete_execution_log(self, execution_log_id: str) -> bool:
        """
        Delete an execution log

        Args:
            execution_log_id: ID of the execution log to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if is_local_dev():
                return self._delete_execution_log_table(execution_log_id)
            else:
                return self._delete_execution_log_sql(execution_log_id)

        except Exception as e:
            logger.error(f"Error deleting execution log: {str(e)}")
            return False

    def _delete_execution_log_table(self, execution_log_id: str) -> bool:
        """Delete execution log from Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return False

            # Find the entity first to get the partition key
            filter_query = f"RowKey eq '{execution_log_id}'"
            entities = list(self.table_repo.query_entities(filter_query))

            if not entities:
                logger.warning(f"Execution log not found: {execution_log_id}")
                return False

            entity = entities[0]
            success = self.table_repo.delete_entity(entity["PartitionKey"], entity["RowKey"])

            if success:
                logger.info(f"Deleted execution log from Table Storage: {execution_log_id}")
                return True
            else:
                logger.error(f"Failed to delete execution log from Table Storage: {execution_log_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting execution log from Table Storage: {str(e)}")
            return False

    def _delete_execution_log_sql(self, execution_log_id: str) -> bool:
        """Delete execution log from SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return False

            query = "DELETE FROM App_ExecutionLog WHERE Id = ?"
            params = (execution_log_id,)

            success = self.sql_repo.execute_non_query(query, params)

            if success:
                logger.info(f"Deleted execution log from SQL Database: {execution_log_id}")
                return True
            else:
                logger.error(f"Failed to delete execution log from SQL Database: {execution_log_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting execution log from SQL Database: {str(e)}")
            return False


# Global service instance
_execution_log_service = None


def get_execution_log_service() -> ExecutionLogService:
    """
    Get the global execution log service instance

    Returns:
        ExecutionLogService: The service instance
    """
    global _execution_log_service

    if _execution_log_service is None:
        _execution_log_service = ExecutionLogService()
        logger.debug("Created global execution log service instance")

    return _execution_log_service
