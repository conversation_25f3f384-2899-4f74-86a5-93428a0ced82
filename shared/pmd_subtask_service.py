"""
PMD Subtask Service

This service manages PMD subtasks for the AtomSec database service.
It handles the creation, retrieval, and management of PMD subtasks based on policies and rules.

Features:
- Get enabled PMD subtasks for integrations
- Manage PMD subtask configuration
- Handle subtask-to-rule relationships
- Support for granular PMD scanning control

This matches the functionality from the dev branch exactly.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List

# Import shared modules
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.common import is_local_dev

# Configure module-level logger
logger = logging.getLogger(__name__)


class PMDSubtaskService:
    """
    Service for managing PMD subtasks
    
    This service provides functionality for managing PMD subtasks that are used
    to control granular PMD scanning based on policies and rules.
    """
    
    def __init__(self):
        """Initialize the PMD subtask service"""
        self.is_local = is_local_dev()
        
        if self.is_local:
            # Use Table Storage for local development
            self.policy_repo = TableStorageRepository("Policy")
            self.rule_repo = TableStorageRepository("Rule")
            self.subtask_repo = TableStorageRepository("PMDSubtask")
            logger.info("Initialized PMD subtask service with Table Storage")
        else:
            # Use SQL Database for production
            self.sql_repo = SqlDatabaseRepository("pmd_subtask_service")
            logger.info("Initialized PMD subtask service with SQL Database")
    
    def get_enabled_pmd_subtasks_for_integration(self, integration_id: str) -> List[Dict[str, Any]]:
        """
        Get enabled PMD subtasks for a specific integration
        
        Args:
            integration_id: Integration ID
        
        Returns:
            List[Dict[str, Any]]: List of enabled PMD subtasks
        """
        try:
            if self.is_local:
                return self._get_enabled_subtasks_table_storage(integration_id)
            else:
                return self._get_enabled_subtasks_sql(integration_id)
                
        except Exception as e:
            logger.error(f"Error getting enabled PMD subtasks for integration {integration_id}: {e}")
            return []
    
    def _get_enabled_subtasks_table_storage(self, integration_id: str) -> List[Dict[str, Any]]:
        """Get enabled subtasks using Table Storage"""
        try:
            # Find PMD policy for this integration
            policies = self.policy_repo.query_entities(f"IntegrationId eq '{integration_id}' and Name eq 'Static Code Analysis (PMD)'")
            
            if not policies:
                logger.warning(f"No PMD policy found for integration {integration_id}")
                return []
            
            policy_id = policies[0].get("PolicyId")
            
            # Find PMD rule for this policy
            rules = self.rule_repo.query_entities(f"PolicyId eq '{policy_id}' and TaskType eq 'pmd_apex_security' and Enabled eq 1")
            
            if not rules:
                logger.warning(f"No enabled PMD rule found for policy {policy_id}")
                return []
            
            rule_id = rules[0].get("RuleId")
            
            # Get enabled subtasks for this rule
            subtasks = self.subtask_repo.query_entities(f"RuleId eq '{rule_id}' and Enabled eq 1")
            
            logger.info(f"Found {len(subtasks)} enabled PMD subtasks for integration {integration_id}")
            return subtasks
            
        except Exception as e:
            logger.error(f"Error getting enabled PMD subtasks from Table Storage: {e}")
            return []
    
    def _get_enabled_subtasks_sql(self, integration_id: str) -> List[Dict[str, Any]]:
        """Get enabled subtasks using SQL Database"""
        try:
            # Find PMD policy for this integration
            policy_query = """
                SELECT PolicyId FROM Policy 
                WHERE IntegrationId = ? AND Name = 'Static Code Analysis (PMD)'
            """
            policies = self.sql_repo.execute_query(policy_query, (integration_id,))
            
            if not policies:
                logger.warning(f"No PMD policy found for integration {integration_id}")
                return []
            
            policy_id = policies[0][0]
            
            # Find PMD rule for this policy
            rule_query = """
                SELECT RuleId FROM Rule 
                WHERE PolicyId = ? AND TaskType = 'pmd_apex_security' AND Enabled = 1
            """
            rules = self.sql_repo.execute_query(rule_query, (policy_id,))
            
            if not rules:
                logger.warning(f"No enabled PMD rule found for policy {policy_id}")
                return []
            
            rule_id = rules[0][0]
            
            # Get enabled subtasks for this rule
            subtask_query = """
                SELECT SubtaskId, SubtaskName, SubtaskDescription, Enabled, CreatedAt
                FROM PMDSubtask 
                WHERE RuleId = ? AND Enabled = 1
            """
            subtasks = self.sql_repo.execute_query(subtask_query, (rule_id,))
            
            # Convert to list of dictionaries
            result = []
            for subtask in subtasks:
                result.append({
                    "SubtaskId": subtask[0],
                    "SubtaskName": subtask[1],
                    "SubtaskDescription": subtask[2],
                    "Enabled": bool(subtask[3]),
                    "CreatedAt": subtask[4]
                })
            
            logger.info(f"Found {len(result)} enabled PMD subtasks for integration {integration_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error getting enabled PMD subtasks from SQL: {e}")
            return []
    
    def get_pmd_configuration_for_integration(self, integration_id: str) -> Dict[str, Any]:
        """
        Get complete PMD configuration for a specific integration
        
        Args:
            integration_id: Integration ID
        
        Returns:
            Dict[str, Any]: Complete PMD configuration
        """
        try:
            if self.is_local:
                return self._get_pmd_config_table_storage(integration_id)
            else:
                return self._get_pmd_config_sql(integration_id)
                
        except Exception as e:
            logger.error(f"Error getting PMD configuration for integration {integration_id}: {e}")
            return {}
    
    def _get_pmd_config_table_storage(self, integration_id: str) -> Dict[str, Any]:
        """Get PMD configuration using Table Storage"""
        try:
            # Find PMD policy for this integration
            policies = self.policy_repo.query_entities(f"IntegrationId eq '{integration_id}' and Name eq 'Static Code Analysis (PMD)'")
            
            if not policies:
                logger.warning(f"No PMD policy found for integration {integration_id}")
                return {}
            
            policy_id = policies[0].get("PolicyId")
            
            # Find PMD rule for this policy
            rules = self.rule_repo.query_entities(f"PolicyId eq '{policy_id}' and TaskType eq 'pmd_apex_security'")
            
            if not rules:
                logger.warning(f"No PMD rule found for policy {policy_id}")
                return {}
            
            rule_id = rules[0].get("RuleId")
            rule_enabled = bool(rules[0].get("Enabled", 0))
            
            # Get all subtasks for this rule
            subtasks = self.subtask_repo.query_entities(f"RuleId eq '{rule_id}'")
            
            # Build complete configuration
            configuration = {
                "integration_id": integration_id,
                "policy_id": policy_id,
                "rule_id": rule_id,
                "pmd_rule_enabled": rule_enabled,
                "subtasks": []
            }
            
            for subtask in subtasks:
                configuration["subtasks"].append({
                    "subtask_id": subtask.get("SubtaskId"),
                    "name": subtask.get("SubtaskName"),
                    "description": subtask.get("SubtaskDescription"),
                    "enabled": bool(subtask.get("Enabled", 0))
                })
            
            logger.info(f"Built PMD configuration for integration {integration_id} with {len(subtasks)} subtasks")
            return configuration
            
        except Exception as e:
            logger.error(f"Error getting PMD configuration from Table Storage: {e}")
            return {}
    
    def _get_pmd_config_sql(self, integration_id: str) -> Dict[str, Any]:
        """Get PMD configuration using SQL Database"""
        try:
            # Find PMD policy for this integration
            policy_query = """
                SELECT PolicyId FROM Policy 
                WHERE IntegrationId = ? AND Name = 'Static Code Analysis (PMD)'
            """
            policies = self.sql_repo.execute_query(policy_query, (integration_id,))
            
            if not policies:
                logger.warning(f"No PMD policy found for integration {integration_id}")
                return {}
            
            policy_id = policies[0][0]
            
            # Find PMD rule for this policy
            rule_query = """
                SELECT RuleId, Enabled FROM Rule 
                WHERE PolicyId = ? AND TaskType = 'pmd_apex_security'
            """
            rules = self.sql_repo.execute_query(rule_query, (policy_id,))
            
            if not rules:
                logger.warning(f"No PMD rule found for policy {policy_id}")
                return {}
            
            rule_id = rules[0][0]
            rule_enabled = bool(rules[0][1])
            
            # Get all subtasks for this rule
            subtask_query = """
                SELECT SubtaskId, SubtaskName, SubtaskDescription, Enabled, CreatedAt
                FROM PMDSubtask 
                WHERE RuleId = ?
            """
            subtasks = self.sql_repo.execute_query(subtask_query, (rule_id,))
            
            # Build complete configuration
            configuration = {
                "integration_id": integration_id,
                "policy_id": policy_id,
                "rule_id": rule_id,
                "pmd_rule_enabled": rule_enabled,
                "subtasks": []
            }
            
            for subtask in subtasks:
                configuration["subtasks"].append({
                    "subtask_id": subtask[0],
                    "name": subtask[1],
                    "description": subtask[2],
                    "enabled": bool(subtask[3]),
                    "created_at": subtask[4]
                })
            
            logger.info(f"Built PMD configuration for integration {integration_id} with {len(subtasks)} subtasks")
            return configuration
            
        except Exception as e:
            logger.error(f"Error getting PMD configuration from SQL: {e}")
            return {}
    
    def create_pmd_subtask(
        self,
        rule_id: str,
        subtask_name: str,
        subtask_description: str = "",
        enabled: bool = True
    ) -> Optional[str]:
        """
        Create a new PMD subtask
        
        Args:
            rule_id: Rule ID this subtask belongs to
            subtask_name: Name of the subtask
            subtask_description: Description of the subtask
            enabled: Whether the subtask is enabled
        
        Returns:
            str: Subtask ID if successful, None otherwise
        """
        try:
            subtask_id = str(uuid.uuid4())
            
            if self.is_local:
                # Create in Table Storage
                entity = {
                    "PartitionKey": rule_id,
                    "RowKey": subtask_id,
                    "SubtaskId": subtask_id,
                    "RuleId": rule_id,
                    "TaskType": "pmd_apex_security",
                    "SubtaskName": subtask_name,
                    "SubtaskDescription": subtask_description,
                    "Enabled": int(enabled),
                    "CreatedAt": datetime.now().isoformat()
                }
                
                success = self.subtask_repo.insert_entity(entity)
                
            else:
                # Create in SQL Database
                query = """
                    INSERT INTO PMDSubtask (SubtaskId, RuleId, TaskType, SubtaskName, SubtaskDescription, Enabled, CreatedAt, UpdatedAt)
                    VALUES (?, ?, ?, ?, ?, ?, GETDATE(), GETDATE())
                """
                params = (subtask_id, rule_id, "pmd_apex_security", subtask_name, subtask_description, int(enabled))
                success = self.sql_repo.execute_non_query(query, params)
            
            if success:
                logger.info(f"Created PMD subtask: {subtask_name} with ID {subtask_id}")
                return subtask_id
            else:
                logger.error(f"Failed to create PMD subtask: {subtask_name}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating PMD subtask: {str(e)}")
            return None


# Global service instance
_pmd_subtask_service = None


def get_pmd_subtask_service() -> PMDSubtaskService:
    """
    Get the global PMD subtask service instance
    
    Returns:
        PMDSubtaskService: The service instance
    """
    global _pmd_subtask_service
    
    if _pmd_subtask_service is None:
        _pmd_subtask_service = PMDSubtaskService()
        logger.debug("Created global PMD subtask service instance")
    
    return _pmd_subtask_service
