"""
Scan Initiation Module

This module provides scan initiation functionality that matches the dev branch exactly.
It handles the complete workflow from scan request to task enqueueing with proper
policy checking, subtask creation, and task orchestration.

Features:
- Complete scan workflow initiation
- Policy-based task enablement
- Subtask creation for PMD scans
- Task orchestration and coordination
- Integration with background processor

This ensures exact parity with the dev branch scan initiation process.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

# Import shared modules
from shared.background_processor import (
    get_background_processor,
    TASK_TYPE_OVERVIEW,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_PROFILES,
    TASK_TYPE_PROFILES_PERMISSION_SETS,
    TASK_TYPE_PERMISSION_SETS,
    TASK_TYPE_METADATA_EXTRACTION,
    TASK_TYPE_PMD_APEX_SECURITY,
    TASK_TYPE_SFDC_AUTHENTICATE,
    TASK_PRIORITY_HIGH,
    TASK_PRIORITY_MEDIUM,
    TASK_PRIORITY_LOW
)
from shared.policy_management import get_policy_management_service
from shared.pmd_subtask_service import get_pmd_subtask_service
from shared.execution_log_service import get_execution_log_service

# Configure module-level logger
logger = logging.getLogger(__name__)


class ScanInitiationService:
    """
    Service for initiating scans that matches the dev branch functionality exactly
    
    This service handles the complete scan initiation workflow including:
    - Authentication verification
    - Metadata extraction
    - Policy-based task enqueueing
    - Subtask creation for PMD scans
    - Task orchestration
    """
    
    def __init__(self):
        """Initialize the scan initiation service"""
        self.background_processor = get_background_processor()
        self.policy_service = get_policy_management_service()
        self.pmd_service = get_pmd_subtask_service()
        self.execution_service = get_execution_log_service()
        
        logger.info("Scan initiation service initialized")
    
    def initiate_complete_scan(
        self,
        integration_id: str,
        user_id: str,
        scan_types: List[str],
        params: Dict[str, Any],
        priority: str = TASK_PRIORITY_MEDIUM
    ) -> Dict[str, Any]:
        """
        Initiate a complete scan workflow matching the dev branch exactly
        
        Args:
            integration_id: Integration ID (org_id)
            user_id: User ID
            scan_types: List of scan types to execute
            params: Common parameters for all tasks
            priority: Task priority
        
        Returns:
            Dict[str, Any]: Scan initiation result
        """
        try:
            logger.info(f"Initiating complete scan for integration {integration_id} with types: {scan_types}")
            
            # Step 1: Verify authentication (always required)
            auth_task_id = self._initiate_authentication(integration_id, user_id, params)
            
            # Do NOT enqueue metadata_extraction or other scan tasks here.
            # All dependent tasks will be enqueued by the SFDC processor after authentication.

            # Step 2: Create execution log for the overall scan
            execution_log_id = self.execution_service.create_execution_log(
                org_id=integration_id,
                execution_type="complete_scan",
                user_id=user_id,
                status="Running"
            )
            
            result = {
                "success": True,
                "scan_id": execution_log_id,
                "integration_id": integration_id,
                "initiated_at": datetime.now().isoformat(),
                "tasks": {
                    "authentication": auth_task_id
                },
                "total_tasks": 1 if auth_task_id else 0
            }
            
            logger.info(f"Successfully initiated scan for integration {integration_id} with {result['total_tasks']} tasks")
            return result
            
        except Exception as e:
            logger.error(f"Error initiating complete scan: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "integration_id": integration_id
            }
    
    def _initiate_authentication(self, integration_id: str, user_id: str, params: Dict[str, Any]) -> Optional[str]:
        """Initiate SFDC authentication task"""
        try:
            # Authentication is always enabled (critical task)
            task_id = self.background_processor.enqueue_task(
                task_type=TASK_TYPE_SFDC_AUTHENTICATE,
                org_id=integration_id,
                user_id=user_id,
                params=params.copy(),
                priority=TASK_PRIORITY_HIGH
            )
            
            if task_id:
                logger.info(f"Enqueued authentication task {task_id} for integration {integration_id}")
            else:
                logger.warning(f"Failed to enqueue authentication task for integration {integration_id}")
            
            return task_id
            
        except Exception as e:
            logger.error(f"Error initiating authentication: {str(e)}")
            return None
    
    def _initiate_metadata_extraction(self, integration_id: str, user_id: str, params: Dict[str, Any]) -> Optional[str]:
        """Initiate metadata extraction task"""
        try:
            # Check for existing pending/running metadata extraction task (idempotency)
            latest_metadata_task = self.background_processor.get_latest_task_for_org(integration_id, TASK_TYPE_METADATA_EXTRACTION)
            
            if latest_metadata_task and latest_metadata_task.get("Status") in ["pending", "running"]:
                logger.info(f"Metadata extraction already in progress for integration {integration_id}: {latest_metadata_task.get('TaskId')}")
                return latest_metadata_task.get("TaskId")
            
            # Metadata extraction is always enabled (critical task)
            task_id = self.background_processor.enqueue_task(
                task_type=TASK_TYPE_METADATA_EXTRACTION,
                org_id=integration_id,
                user_id=user_id,
                params=params.copy(),
                priority=TASK_PRIORITY_HIGH
            )
            
            if task_id:
                logger.info(f"Enqueued metadata extraction task {task_id} for integration {integration_id}")
            else:
                logger.warning(f"Failed to enqueue metadata extraction task for integration {integration_id}")
            
            return task_id
            
        except Exception as e:
            logger.error(f"Error initiating metadata extraction: {str(e)}")
            return None
    
    def _enqueue_scan_tasks(
        self,
        integration_id: str,
        user_id: str,
        scan_types: List[str],
        params: Dict[str, Any],
        priority: str
    ) -> List[str]:
        """Enqueue scan tasks based on scan types and policies"""
        try:
            task_ids = []
            
            # Map scan types to task types (matching dev branch exactly)
            task_type_map = {
                "overview": TASK_TYPE_OVERVIEW,
                "health_check": TASK_TYPE_HEALTH_CHECK,
                "profiles": TASK_TYPE_PROFILES,
                "profiles_permission_sets": TASK_TYPE_PROFILES_PERMISSION_SETS,
                "permission_sets": TASK_TYPE_PERMISSION_SETS,
                "pmd_apex_security": TASK_TYPE_PMD_APEX_SECURITY
            }
            
            # Enqueue individual scan tasks
            for scan_type in scan_types:
                task_type = task_type_map.get(scan_type)
                if not task_type:
                    logger.warning(f"Unknown scan type: {scan_type}")
                    continue
                
                # Prepare task-specific parameters
                task_params = params.copy()
                
                # For PMD tasks, add subtask configuration
                if task_type == TASK_TYPE_PMD_APEX_SECURITY:
                    task_params = self._prepare_pmd_task_params(integration_id, task_params)
                
                # Enqueue the task (policy checking is done in background processor)
                task_id = self.background_processor.enqueue_task(
                    task_type=task_type,
                    org_id=integration_id,
                    user_id=user_id,
                    params=task_params,
                    priority=priority
                )
                
                if task_id:
                    task_ids.append(task_id)
                    logger.info(f"Enqueued {scan_type} task {task_id} for integration {integration_id}")
                else:
                    logger.info(f"Skipped {scan_type} task for integration {integration_id} (not enabled in policy or already running)")
            
            return task_ids
            
        except Exception as e:
            logger.error(f"Error enqueuing scan tasks: {str(e)}")
            return []
    
    def _prepare_pmd_task_params(self, integration_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare PMD task parameters with subtask configuration"""
        try:
            # Get enabled PMD subtasks for this integration
            enabled_subtasks = self.pmd_service.get_enabled_pmd_subtasks_for_integration(integration_id)
            
            # Extract scan categories from enabled subtasks
            scan_categories = []
            enabled_individual_rules = []
            
            for subtask in enabled_subtasks:
                subtask_name = subtask.get("SubtaskName", "")
                if subtask_name and subtask.get("Enabled"):
                    scan_categories.append(subtask_name)
                    
                    # Map subtask names to PMD rule categories
                    if "security" in subtask_name.lower():
                        enabled_individual_rules.extend(["Security", "ApexSecurity"])
                    elif "best" in subtask_name.lower() and "practice" in subtask_name.lower():
                        enabled_individual_rules.extend(["BestPractices", "CodeStyle"])
                    elif "performance" in subtask_name.lower():
                        enabled_individual_rules.extend(["Performance"])
            
            # Add PMD-specific parameters
            params["scan_categories"] = scan_categories or ["Security", "BestPractices", "CodeStyle"]
            params["enabled_individual_rules"] = enabled_individual_rules or ["Security", "BestPractices"]
            params["enabled_subtasks"] = enabled_subtasks
            
            logger.info(f"Prepared PMD task params with {len(scan_categories)} categories and {len(enabled_individual_rules)} rules")
            return params
            
        except Exception as e:
            logger.error(f"Error preparing PMD task params: {str(e)}")
            # Return default parameters on error
            params["scan_categories"] = ["Security", "BestPractices", "CodeStyle"]
            params["enabled_individual_rules"] = ["Security", "BestPractices"]
            return params
    
    def initiate_policy_based_scan(
        self,
        integration_id: str,
        user_id: str,
        policy_name: str,
        params: Dict[str, Any],
        priority: str = TASK_PRIORITY_MEDIUM
    ) -> Dict[str, Any]:
        """
        Initiate a scan based on a specific policy
        
        Args:
            integration_id: Integration ID
            user_id: User ID
            policy_name: Name of the policy to execute
            params: Task parameters
            priority: Task priority
        
        Returns:
            Dict[str, Any]: Scan initiation result
        """
        try:
            logger.info(f"Initiating policy-based scan for policy '{policy_name}' on integration {integration_id}")
            
            # Use policy management service to enqueue tasks
            self.policy_service.enqueue_policy_tasks(
                processor=self.background_processor,
                org_id=integration_id,
                user_id=user_id,
                policy_name=policy_name,
                params=params,
                priority=priority
            )
            
            return {
                "success": True,
                "policy_name": policy_name,
                "integration_id": integration_id,
                "initiated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error initiating policy-based scan: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "policy_name": policy_name,
                "integration_id": integration_id
            }


# Global service instance
_scan_initiation_service = None


def get_scan_initiation_service() -> ScanInitiationService:
    """
    Get the global scan initiation service instance
    
    Returns:
        ScanInitiationService: The service instance
    """
    global _scan_initiation_service
    
    if _scan_initiation_service is None:
        _scan_initiation_service = ScanInitiationService()
        logger.debug("Created global scan initiation service instance")
    
    return _scan_initiation_service
