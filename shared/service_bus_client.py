"""
Service Bus Client for Event Publishing

This module provides a client for publishing events to Azure Service Bus.
It enables event-driven communication between the DB service and SFDC service.
"""

import logging
import json
import os
from typing import Dict, Any, Optional
from datetime import datetime
from azure.servicebus import ServiceBusClient, ServiceBusMessage
from azure.servicebus.exceptions import ServiceBusError

logger = logging.getLogger(__name__)

class ServiceBusEventPublisher:
    """Client for publishing events to Azure Service Bus"""
    
    def __init__(self):
        """Initialize the Service Bus client"""
        self.connection_string = self._get_connection_string()
        self.topic_name = "atomsec-events"
        self.client = None
        
        if self.connection_string:
            try:
                self.client = ServiceBusClient.from_connection_string(self.connection_string)
                logger.info("Service Bus client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Service Bus client: {str(e)}")
                self.client = None
    
    def _get_connection_string(self) -> Optional[str]:
        """Get Service Bus connection string from environment"""
        return os.environ.get('AzureServiceBusConnectionString')
    
    def publish_event(self, event_type: str, event_data: Dict[str, Any], 
                     user_id: Optional[str] = None, integration_id: Optional[str] = None) -> bool:
        """
        Publish an event to Service Bus
        
        Args:
            event_type: Type of event (e.g., 'TaskCreated', 'IntegrationUpdated')
            event_data: Event data payload
            user_id: Optional user ID for user-specific events
            integration_id: Optional integration ID for integration-specific events
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.client:
            logger.warning("Service Bus client not available, skipping event publishing")
            return False
        
        try:
            # Create event message
            event_message = {
                "eventType": event_type,
                "eventId": f"{event_type}_{datetime.now().isoformat()}",
                "timestamp": datetime.now().isoformat(),
                "userId": user_id,
                "integrationId": integration_id,
                "data": event_data
            }
            
            # Create Service Bus message
            message = ServiceBusMessage(
                body=json.dumps(event_message),
                content_type="application/json"
            )
            
            # Add custom properties
            message.application_properties = {
                "eventType": event_type,
                "userId": user_id or "",
                "integrationId": integration_id or "",
                "timestamp": event_message["timestamp"]
            }
            
            # Publish to topic
            with self.client.get_topic_sender(topic_name=self.topic_name) as sender:
                sender.send_messages(message)
            
            logger.info(f"Successfully published {event_type} event")
            return True
            
        except ServiceBusError as e:
            logger.error(f"Service Bus error publishing {event_type} event: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error publishing {event_type} event: {str(e)}")
            return False
    
    def publish_task_event(self, task_id: str, task_type: str, org_id: str, 
                          user_id: str, status: str, priority: str = "medium",
                          params: Optional[Dict[str, Any]] = None) -> bool:
        """
        Publish task-related events
        
        Args:
            task_id: Task ID
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID
            status: Task status
            priority: Task priority
            params: Task parameters
            
        Returns:
            bool: True if successful, False otherwise
        """
        event_data = {
            "task_id": task_id,
            "task_type": task_type,
            "org_id": org_id,
            "user_id": user_id,
            "status": status,
            "priority": priority,
            "params": params or {}
        }
        
        return self.publish_event("TaskCreated", event_data, user_id, org_id)
    
    def publish_integration_event(self, integration_id: str, event_type: str,
                                 user_id: str, integration_data: Dict[str, Any]) -> bool:
        """
        Publish integration-related events
        
        Args:
            integration_id: Integration ID
            event_type: Type of event (IntegrationCreated, IntegrationUpdated, etc.)
            user_id: User ID
            integration_data: Integration data
            
        Returns:
            bool: True if successful, False otherwise
        """
        event_data = {
            "integration_id": integration_id,
            "integration_data": integration_data
        }
        
        return self.publish_event(event_type, event_data, user_id, integration_id)
    
    def publish_user_event(self, user_id: str, event_type: str, user_data: Dict[str, Any]) -> bool:
        """
        Publish user-related events
        
        Args:
            user_id: User ID
            event_type: Type of event (UserCreated, UserUpdated, etc.)
            user_data: User data
            
        Returns:
            bool: True if successful, False otherwise
        """
        event_data = {
            "user_id": user_id,
            "user_data": user_data
        }
        
        return self.publish_event(event_type, event_data, user_id)
    
    def publish_security_event(self, integration_id: str, event_type: str,
                              user_id: str, security_data: Dict[str, Any]) -> bool:
        """
        Publish security-related events
        
        Args:
            integration_id: Integration ID
            event_type: Type of event (SecurityScanCompleted, etc.)
            user_id: User ID
            security_data: Security scan data
            
        Returns:
            bool: True if successful, False otherwise
        """
        event_data = {
            "integration_id": integration_id,
            "security_data": security_data
        }
        
        return self.publish_event(event_type, event_data, user_id, integration_id)

    def send_task_message(self, task_data: Dict[str, Any]) -> bool:
        """
        Send task message to Service Bus (compatibility method)

        Args:
            task_data: Task data dictionary

        Returns:
            bool: True if successful, False otherwise
        """
        return self.publish_task_event(
            task_data.get("task_id", ""),
            task_data.get("task_type", ""),
            task_data.get("org_id", ""),
            task_data.get("user_id", ""),
            "created",  # Status for new tasks
            task_data.get("priority", "medium"),
            task_data.get("params", {})
        )

# Global client instance
_service_bus_client = None

def get_service_bus_client() -> Optional[ServiceBusEventPublisher]:
    """Get or create Service Bus client instance"""
    global _service_bus_client
    if _service_bus_client is None:
        _service_bus_client = ServiceBusEventPublisher()
    return _service_bus_client

def get_db_service_bus_client() -> Optional[ServiceBusEventPublisher]:
    """Get or create DB Service Bus client instance (alias for compatibility)"""
    return get_service_bus_client()
