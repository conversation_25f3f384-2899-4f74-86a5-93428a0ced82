"""
SFDC Service Client

This module provides a client for communicating with the SFDC service.
It handles HTTP requests to the SFDC service endpoints and manages authentication.
"""

import logging
import requests
import json
from typing import Dict, Any, Optional, List
from shared.config import is_local_dev
import os

logger = logging.getLogger(__name__)

class SFDCServiceClient:
    """Client for communicating with the SFDC service"""
    
    def __init__(self):
        """Initialize the SFDC service client"""
        self.base_url = self._get_sfdc_service_url()
        self.timeout = 120  # 2 minutes timeout for SFDC operations
        
    def _get_sfdc_service_url(self) -> str:
        """Get the SFDC service URL based on environment"""
        if is_local_dev():
            return os.environ.get('SFDC_SERVICE_URL', 'http://localhost:7071')
        else:
            # In production, use the Azure API Management URL for SFDC service
            return os.environ.get('SFDC_SERVICE_URL', 'https://apim-atomsec-dev.azure-api.net')
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None, 
                     params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """
        Make HTTP request to SFDC service
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without leading slash)
            data: Request body data
            params: Query parameters
            headers: Additional headers
            
        Returns:
            Response data or None if error
        """
        try:
            url = f"{self.base_url}/api/{endpoint}"
            
            # Default headers
            request_headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # Add additional headers if provided
            if headers:
                request_headers.update(headers)
            
            logger.info(f"Making {method} request to SFDC service: {url}")
            
            # Make the request
            if method.upper() == 'GET':
                response = requests.get(url, params=params, headers=request_headers, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = requests.post(url, json=data, params=params, headers=request_headers, timeout=self.timeout)
            elif method.upper() == 'PUT':
                response = requests.put(url, json=data, params=params, headers=request_headers, timeout=self.timeout)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, params=params, headers=request_headers, timeout=self.timeout)
            else:
                logger.error(f"Unsupported HTTP method: {method}")
                return None
            
            # Check response status
            response.raise_for_status()
            
            # Try to parse JSON response
            try:
                return response.json()
            except json.JSONDecodeError:
                # Return text response if not JSON
                return {"text": response.text, "status_code": response.status_code}
                
        except requests.exceptions.Timeout:
            logger.error(f"Timeout error calling SFDC service endpoint: {endpoint}")
            return None
        except requests.exceptions.ConnectionError:
            logger.error(f"Connection error calling SFDC service endpoint: {endpoint}")
            return None
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error calling SFDC service endpoint {endpoint}: {e}")
            try:
                error_response = response.json()
                logger.error(f"Error response: {error_response}")
                return error_response
            except:
                return {"error": str(e), "status_code": response.status_code}
        except Exception as e:
            logger.error(f"Unexpected error calling SFDC service endpoint {endpoint}: {e}")
            return None
    
    # Integration scan methods
    def scan_integration(self, integration_id: str, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Trigger integration scan"""
        return self._make_request('POST', f'integrations/{integration_id}/scan', headers=headers)
    
    # Task management methods
    def get_task_status(self, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get task status"""
        return self._make_request('GET', 'task-status', params=params, headers=headers)
    
    def cancel_task(self, data: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Cancel a task"""
        return self._make_request('POST', 'tasks/cancel', data=data, headers=headers)
    
    def schedule_task(self, data: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Schedule a task"""
        return self._make_request('POST', 'tasks/schedule', data=data, headers=headers)
    
    # Security analysis methods
    def get_health_score(self, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get health score"""
        return self._make_request('GET', 'health-score', params=params, headers=headers)
    
    def get_health_risks(self, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get health risks"""
        return self._make_request('GET', 'health-risks', params=params, headers=headers)
    
    def get_profiles(self, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get profiles"""
        return self._make_request('GET', 'profiles', params=params, headers=headers)
    
    def get_permission_sets(self, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get permission sets"""
        return self._make_request('GET', 'permission-sets', params=params, headers=headers)
    
    # Scan results methods
    def get_scan_accounts(self, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get scan accounts"""
        return self._make_request('GET', 'scan/accounts', params=params, headers=headers)
    
    def get_scan_history(self, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get scan history"""
        return self._make_request('GET', 'scan/history', params=params, headers=headers)
    
    # Integration connection methods
    def test_connection(self, data: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Test Salesforce connection via SFDC service"""
        return self._make_request('POST', 'integration/test-connection', data=data, headers=headers)

    # System methods
    def get_health(self, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get SFDC service health"""
        return self._make_request('GET', 'health', headers=headers)

    def get_info(self, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Get SFDC service info"""
        return self._make_request('POST', 'info', headers=headers)

# Global client instance
_sfdc_client = None

def get_sfdc_client() -> SFDCServiceClient:
    """Get or create SFDC service client instance"""
    global _sfdc_client
    if _sfdc_client is None:
        _sfdc_client = SFDCServiceClient()
    return _sfdc_client
